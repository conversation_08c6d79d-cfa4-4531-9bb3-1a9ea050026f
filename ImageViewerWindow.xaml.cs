﻿using Serilog;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Imaging;

namespace Yidev.LocalAI
{
    public partial class ImageViewerWindow : Window
    {
        private string _imageUrl;
        private bool _isFitToWindowMode = true; // 跟踪当前是否为适合窗口模式

        public ImageViewerWindow(string imageUrl)
        {
            InitializeComponent();
            _imageUrl = imageUrl;

            // 监听窗口大小变化
            this.SizeChanged += ImageViewerWindow_SizeChanged;

            LoadImage();
        }

        private void ImageViewerWindow_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            // 如果当前是适合窗口模式，则重新调整图片大小
            if (_isFitToWindowMode && MainImage.Source != null)
            {
                // 延迟执行，确保UI更新完成
                // 使用更高的优先级并增加延迟，确保ScrollViewer尺寸已更新
                Dispatcher.BeginInvoke(new Action(() => {
                    Debug.WriteLine($"窗口大小变化: {e.NewSize.Width}x{e.NewSize.Height}");
                    Debug.WriteLine($"ScrollViewer尺寸: {ImageScrollViewer.ActualWidth}x{ImageScrollViewer.ActualHeight}");
                    FitToWindow();
                }), System.Windows.Threading.DispatcherPriority.Render);
            }
        }

        private async void LoadImage()
        {
            try
            {
                // 显示加载指示器
                LoadingIndicator.Visibility = Visibility.Visible;
                ErrorIndicator.Visibility = Visibility.Collapsed;
                MainImage.Visibility = Visibility.Collapsed;

                // 创建BitmapImage并加载图片
                var bitmap = new BitmapImage();
                bitmap.BeginInit();
                bitmap.UriSource = new Uri(_imageUrl);
                bitmap.CacheOption = BitmapCacheOption.OnLoad;
                bitmap.EndInit();

                // 等待图片加载完成
                if (bitmap.IsDownloading)
                {
                    bitmap.DownloadCompleted += (s, e) =>
                    {
                        Dispatcher.Invoke(() =>
                        {
                            OnImageLoaded(bitmap);
                        });
                    };

                    bitmap.DownloadFailed += (s, e) =>
                    {
                        Dispatcher.Invoke(() =>
                        {
                            OnImageLoadFailed(e.ErrorException);
                        });
                    };
                }
                else
                {
                    OnImageLoaded(bitmap);
                }
            }
            catch (Exception ex)
            {
                OnImageLoadFailed(ex);
            }
        }

        private void OnImageLoaded(BitmapImage bitmap)
        {
            try
            {
                MainImage.Source = bitmap;

                // 隐藏加载指示器，显示图片
                LoadingIndicator.Visibility = Visibility.Collapsed;
                ErrorIndicator.Visibility = Visibility.Collapsed;
                MainImage.Visibility = Visibility.Visible;

                // 更新窗口标题
                Title = $"图片查看器 - {System.IO.Path.GetFileName(_imageUrl)}";

                Debug.WriteLine($"图片加载完成: {bitmap.PixelWidth}x{bitmap.PixelHeight}");

                // 延迟执行适合窗口，确保UI完全渲染完成
                Dispatcher.BeginInvoke(new Action(() => {
                    Debug.WriteLine("开始执行适合窗口操作");
                    FitToWindow();
                }), System.Windows.Threading.DispatcherPriority.Loaded);
            }
            catch (Exception ex)
            {
                OnImageLoadFailed(ex);
            }
        }

        private void OnImageLoadFailed(Exception exception)
        {
            // 隐藏加载指示器和图片，显示错误信息
            LoadingIndicator.Visibility = Visibility.Collapsed;
            MainImage.Visibility = Visibility.Collapsed;
            ErrorIndicator.Visibility = Visibility.Visible;

            // 设置错误消息
            ErrorMessage.Text = $"无法加载图片：{exception?.Message ?? "未知错误"}";

            // 更新窗口标题
            Title = "图片查看器 - 加载失败";
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void FitToWindow_Click(object sender, RoutedEventArgs e)
        {
            _isFitToWindowMode = true;
            FitToWindow();
        }

        private void ActualSize_Click(object sender, RoutedEventArgs e)
        {
            _isFitToWindowMode = false; // 切换到实际大小模式

            if (MainImage.Source == null) return;

            try
            {
                // 获取图片的DPI信息
                BitmapSource bitmapSource = MainImage.Source as BitmapSource;
                if (bitmapSource == null)
                {
                    SetImageScale(1.0);
                    return;
                }

                // 计算DPI缩放因子
                double dpiScaleX = 96.0 / bitmapSource.DpiX;
                double dpiScaleY = 96.0 / bitmapSource.DpiY;

                // 使用DPI缩放因子来显示图片的实际大小
                double scale = Math.Min(dpiScaleX, dpiScaleY);

                Debug.WriteLine($"实际大小缩放比例: {scale}, DPI: {bitmapSource.DpiX}x{bitmapSource.DpiY}");

                SetImageScale(1.0 * scale);

                // 实际大小模式下，通常需要滚动条
                ImageScrollViewer.HorizontalScrollBarVisibility = ScrollBarVisibility.Auto;
                ImageScrollViewer.VerticalScrollBarVisibility = ScrollBarVisibility.Auto;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ActualSize_Click error: {ex.Message}");
                // 出错时使用默认缩放
                SetImageScale(1.0);
            }
        }

        private void OpenInBrowser_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Process.Start(new ProcessStartInfo
                {
                    FileName = _imageUrl,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"无法在浏览器中打开图片：{ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void FitToWindow()
        {
            if (MainImage.Source == null) return;

            try
            {
                // 获取图片的原始尺寸（使用BitmapSource可以获取更准确的尺寸）
                BitmapSource bitmapSource = MainImage.Source as BitmapSource;
                if (bitmapSource == null)
                {
                    Debug.WriteLine("无法将图片源转换为BitmapSource");
                    return;
                }

                // 获取图片的物理像素尺寸
                double imageWidth = bitmapSource.PixelWidth;
                double imageHeight = bitmapSource.PixelHeight;

                // 考虑DPI
                double dpiScaleX = bitmapSource.DpiX / 96.0;
                double dpiScaleY = bitmapSource.DpiY / 96.0;

                // 应用DPI缩放获取实际显示尺寸
                imageWidth /= dpiScaleX;
                imageHeight /= dpiScaleY;

                Debug.WriteLine($"图片尺寸: {imageWidth}x{imageHeight}, DPI: {bitmapSource.DpiX}x{bitmapSource.DpiY}");

                // 获取ScrollViewer的可用空间
                var availableWidth = ImageScrollViewer.ActualWidth;
                var availableHeight = ImageScrollViewer.ActualHeight;

                Debug.WriteLine($"ScrollViewer尺寸: {ImageScrollViewer.ActualWidth}x{ImageScrollViewer.ActualHeight}");
                Debug.WriteLine($"可用空间: {availableWidth}x{availableHeight}");

                if (availableWidth <= 0 || availableHeight <= 0)
                {
                    Debug.WriteLine("ScrollViewer尺寸无效，延迟重试");
                    // 如果ScrollViewer尺寸还没有更新，延迟重试
                    Dispatcher.BeginInvoke(new Action(() => FitToWindow()), System.Windows.Threading.DispatcherPriority.Background);
                    return;
                }

                // 计算缩放比例，确保图片完全适合可视区域
                var scaleX = availableWidth / imageWidth;
                var scaleY = availableHeight / imageHeight;
                var scale = Math.Min(scaleX, scaleY);

                // 限制最小和最大缩放比例
                scale = Math.Max(0.1, Math.Min(scale, 1.0)); // 最大缩放限制为1.0（原始大小）

                Debug.WriteLine($"计算的缩放比例: {scale} (scaleX: {scaleX}, scaleY: {scaleY})");

                // 应用缩放
                SetImageScale(scale);

                // 确保图片从左上角开始显示
                ImageScrollViewer.ScrollToHorizontalOffset(0);
                ImageScrollViewer.ScrollToVerticalOffset(0);

                // 根据缩放比例设置滚动条可见性
                // 如果图片完全适合窗口（缩放比例小于1），则隐藏滚动条
                if (scale < 1.0)
                {
                    ImageScrollViewer.HorizontalScrollBarVisibility = ScrollBarVisibility.Hidden;
                    ImageScrollViewer.VerticalScrollBarVisibility = ScrollBarVisibility.Hidden;
                }
                else
                {
                    ImageScrollViewer.HorizontalScrollBarVisibility = ScrollBarVisibility.Auto;
                    ImageScrollViewer.VerticalScrollBarVisibility = ScrollBarVisibility.Auto;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"FitToWindow error: {ex.Message}");
            }
        }

        // 窗口大小改变时重新适合窗口
        protected override void OnRenderSizeChanged(SizeChangedInfo sizeInfo)
        {
            base.OnRenderSizeChanged(sizeInfo);
            
            // 延迟执行以确保布局完成
            Dispatcher.BeginInvoke(new Action(() =>
            {
                if (MainImage.Visibility == Visibility.Visible && MainImage.Source != null)
                {
                    FitToWindow();
                }
            }), System.Windows.Threading.DispatcherPriority.Loaded);
        }

        private void SetImageScale(double scale)
        {
            if (MainImage.Source == null) return;

            try
            {
                // 创建缩放变换
                var scaleTransform = new ScaleTransform(scale, scale);
                MainImage.RenderTransform = scaleTransform;

                // 设置变换原点为左上角 (0,0)，这样图片会从左上角开始缩放
                MainImage.RenderTransformOrigin = new Point(0, 0);

                Debug.WriteLine($"设置图片缩放: {scale}, 变换原点: (0,0)");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"SetImageScale error: {ex.Message}");
            }
        }

        // 支持ESC键关闭窗口
        protected override void OnKeyDown(System.Windows.Input.KeyEventArgs e)
        {
            if (e.Key == System.Windows.Input.Key.Escape)
            {
                Close();
            }
            base.OnKeyDown(e);
        }
    }
}
