2025-06-24 14:42:20.655 +08:00 [INF] Getting messages for topic ID: 6
2025-06-24 14:42:20.687 +08:00 [DBG] info: 2025/6/24 14:42:20.687 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (22ms) [Parameters=[@__topicId_0='6'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-24 14:42:20.691 +08:00 [INF] Setting conversation history. Message count: 6
2025-06-24 16:44:49.135 +08:00 [INF] Application Shutting Down
2025-06-24 16:45:22.682 +08:00 [INF] Application Starting Up
2025-06-24 16:45:27.289 +08:00 [DBG] warn: 2025/6/24 16:45:27.289 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-24 16:45:27.449 +08:00 [DBG] info: 2025/6/24 16:45:27.449 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-24 16:45:27.454 +08:00 [INF] TopicService initialized and database ensured.
2025-06-24 16:45:28.244 +08:00 [INF] Initializing SemanticKernelService...
2025-06-24 16:45:28.687 +08:00 [INF] SemanticKernelService initialized.
2025-06-24 16:45:35.266 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-24 16:45:35.272 +08:00 [INF] Getting topics for user: llk
2025-06-24 16:45:35.788 +08:00 [DBG] info: 2025/6/24 16:45:35.788 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-24 16:45:35.849 +08:00 [INF] Getting messages for topic ID: 7
2025-06-24 16:45:35.864 +08:00 [DBG] info: 2025/6/24 16:45:35.864 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-24 16:45:35.885 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-24 16:47:59.109 +08:00 [INF] Application Shutting Down
2025-06-24 16:48:03.983 +08:00 [INF] Application Starting Up
2025-06-24 16:48:05.014 +08:00 [DBG] warn: 2025/6/24 16:48:05.013 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-24 16:48:05.180 +08:00 [DBG] info: 2025/6/24 16:48:05.180 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-24 16:48:05.186 +08:00 [INF] TopicService initialized and database ensured.
2025-06-24 16:48:05.359 +08:00 [INF] Initializing SemanticKernelService...
2025-06-24 16:48:05.378 +08:00 [INF] SemanticKernelService initialized.
2025-06-24 16:48:11.992 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-24 16:48:11.997 +08:00 [INF] Getting topics for user: llk
2025-06-24 16:48:12.511 +08:00 [DBG] info: 2025/6/24 16:48:12.511 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (6ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-24 16:48:12.579 +08:00 [INF] Getting messages for topic ID: 7
2025-06-24 16:48:12.596 +08:00 [DBG] info: 2025/6/24 16:48:12.596 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-24 16:48:12.620 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-24 16:48:30.579 +08:00 [INF] Application Shutting Down
2025-06-24 16:49:09.470 +08:00 [INF] Application Starting Up
2025-06-24 16:49:10.523 +08:00 [DBG] warn: 2025/6/24 16:49:10.523 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-24 16:49:10.692 +08:00 [DBG] info: 2025/6/24 16:49:10.692 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-24 16:49:10.698 +08:00 [INF] TopicService initialized and database ensured.
2025-06-24 16:49:10.868 +08:00 [INF] Initializing SemanticKernelService...
2025-06-24 16:49:10.887 +08:00 [INF] SemanticKernelService initialized.
2025-06-24 16:49:17.303 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-24 16:49:17.308 +08:00 [INF] Getting topics for user: llk
2025-06-24 16:49:17.814 +08:00 [DBG] info: 2025/6/24 16:49:17.814 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-24 16:49:17.874 +08:00 [INF] Getting messages for topic ID: 7
2025-06-24 16:49:17.889 +08:00 [DBG] info: 2025/6/24 16:49:17.889 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-24 16:49:17.911 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-24 16:49:19.891 +08:00 [INF] Application Shutting Down
2025-06-24 16:53:02.054 +08:00 [INF] Application Starting Up
2025-06-24 16:53:03.169 +08:00 [DBG] warn: 2025/6/24 16:53:03.169 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-24 16:53:03.337 +08:00 [DBG] info: 2025/6/24 16:53:03.337 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-24 16:53:03.342 +08:00 [INF] TopicService initialized and database ensured.
2025-06-24 16:53:03.510 +08:00 [INF] Initializing SemanticKernelService...
2025-06-24 16:53:03.529 +08:00 [INF] SemanticKernelService initialized.
2025-06-24 16:53:07.727 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-24 16:53:07.733 +08:00 [INF] Getting topics for user: llk
2025-06-24 16:53:08.231 +08:00 [DBG] info: 2025/6/24 16:53:08.231 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-24 16:53:08.291 +08:00 [INF] Getting messages for topic ID: 7
2025-06-24 16:53:08.306 +08:00 [DBG] info: 2025/6/24 16:53:08.306 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-24 16:53:08.328 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-24 16:53:48.527 +08:00 [INF] Application Shutting Down
2025-06-24 16:56:25.976 +08:00 [INF] Application Starting Up
2025-06-24 16:56:27.044 +08:00 [DBG] warn: 2025/6/24 16:56:27.043 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-24 16:56:27.212 +08:00 [DBG] info: 2025/6/24 16:56:27.212 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-24 16:56:27.218 +08:00 [INF] TopicService initialized and database ensured.
2025-06-24 16:56:27.386 +08:00 [INF] Initializing SemanticKernelService...
2025-06-24 16:56:27.404 +08:00 [INF] SemanticKernelService initialized.
2025-06-24 16:56:34.073 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-24 16:56:34.078 +08:00 [INF] Getting topics for user: llk
2025-06-24 16:56:34.578 +08:00 [DBG] info: 2025/6/24 16:56:34.578 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-24 16:56:34.637 +08:00 [INF] Getting messages for topic ID: 7
2025-06-24 16:56:34.651 +08:00 [DBG] info: 2025/6/24 16:56:34.651 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-24 16:56:34.674 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-24 16:58:56.260 +08:00 [INF] Application Shutting Down
2025-06-24 16:59:09.950 +08:00 [INF] Application Starting Up
2025-06-24 16:59:11.009 +08:00 [DBG] warn: 2025/6/24 16:59:11.009 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-24 16:59:11.174 +08:00 [DBG] info: 2025/6/24 16:59:11.174 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-24 16:59:11.180 +08:00 [INF] TopicService initialized and database ensured.
2025-06-24 16:59:11.352 +08:00 [INF] Initializing SemanticKernelService...
2025-06-24 16:59:11.370 +08:00 [INF] SemanticKernelService initialized.
2025-06-24 16:59:17.934 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-24 16:59:17.939 +08:00 [INF] Getting topics for user: llk
2025-06-24 16:59:18.445 +08:00 [DBG] info: 2025/6/24 16:59:18.445 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-24 16:59:18.504 +08:00 [INF] Getting messages for topic ID: 7
2025-06-24 16:59:18.519 +08:00 [DBG] info: 2025/6/24 16:59:18.519 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-24 16:59:18.541 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-24 17:02:49.572 +08:00 [INF] Application Shutting Down
2025-06-24 17:05:41.119 +08:00 [INF] Application Starting Up
2025-06-24 17:05:42.249 +08:00 [DBG] warn: 2025/6/24 17:05:42.249 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-24 17:05:42.421 +08:00 [DBG] info: 2025/6/24 17:05:42.421 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-24 17:05:42.427 +08:00 [INF] TopicService initialized and database ensured.
2025-06-24 17:05:42.605 +08:00 [INF] Initializing SemanticKernelService...
2025-06-24 17:05:42.625 +08:00 [INF] SemanticKernelService initialized.
2025-06-24 17:05:48.353 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-24 17:05:48.359 +08:00 [INF] Getting topics for user: llk
2025-06-24 17:05:48.877 +08:00 [DBG] info: 2025/6/24 17:05:48.877 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-24 17:05:48.937 +08:00 [INF] Getting messages for topic ID: 7
2025-06-24 17:05:48.953 +08:00 [DBG] info: 2025/6/24 17:05:48.953 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-24 17:05:48.975 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-24 17:06:08.378 +08:00 [INF] Application Shutting Down
2025-06-24 17:06:12.452 +08:00 [INF] Application Starting Up
2025-06-24 17:06:13.486 +08:00 [DBG] warn: 2025/6/24 17:06:13.486 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-24 17:06:13.649 +08:00 [DBG] info: 2025/6/24 17:06:13.649 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (10ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-24 17:06:13.654 +08:00 [INF] TopicService initialized and database ensured.
2025-06-24 17:06:13.828 +08:00 [INF] Initializing SemanticKernelService...
2025-06-24 17:06:13.847 +08:00 [INF] SemanticKernelService initialized.
2025-06-24 17:06:19.286 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-24 17:06:19.292 +08:00 [INF] Getting topics for user: llk
2025-06-24 17:06:19.813 +08:00 [DBG] info: 2025/6/24 17:06:19.813 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-24 17:06:19.874 +08:00 [INF] Getting messages for topic ID: 7
2025-06-24 17:06:19.890 +08:00 [DBG] info: 2025/6/24 17:06:19.890 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-24 17:06:19.913 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-24 17:06:57.093 +08:00 [INF] Application Shutting Down
2025-06-24 17:10:36.558 +08:00 [INF] Application Starting Up
2025-06-24 17:10:37.628 +08:00 [DBG] warn: 2025/6/24 17:10:37.627 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-24 17:10:37.800 +08:00 [DBG] info: 2025/6/24 17:10:37.800 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-24 17:10:37.806 +08:00 [INF] TopicService initialized and database ensured.
2025-06-24 17:10:37.977 +08:00 [INF] Initializing SemanticKernelService...
2025-06-24 17:10:37.998 +08:00 [INF] SemanticKernelService initialized.
2025-06-24 17:10:43.354 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-24 17:10:43.359 +08:00 [INF] Getting topics for user: llk
2025-06-24 17:10:43.876 +08:00 [DBG] info: 2025/6/24 17:10:43.876 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (6ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-24 17:10:43.942 +08:00 [INF] Getting messages for topic ID: 7
2025-06-24 17:10:43.957 +08:00 [DBG] info: 2025/6/24 17:10:43.957 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-24 17:10:43.979 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-24 17:11:12.272 +08:00 [INF] Application Shutting Down
2025-06-24 17:12:56.582 +08:00 [INF] Application Starting Up
2025-06-24 17:12:57.679 +08:00 [DBG] warn: 2025/6/24 17:12:57.678 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-24 17:12:57.850 +08:00 [DBG] info: 2025/6/24 17:12:57.850 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-24 17:12:57.856 +08:00 [INF] TopicService initialized and database ensured.
2025-06-24 17:12:58.030 +08:00 [INF] Initializing SemanticKernelService...
2025-06-24 17:12:58.051 +08:00 [INF] SemanticKernelService initialized.
2025-06-24 17:13:04.791 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-24 17:13:04.797 +08:00 [INF] Getting topics for user: llk
2025-06-24 17:13:05.318 +08:00 [DBG] info: 2025/6/24 17:13:05.318 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-24 17:13:05.378 +08:00 [INF] Getting messages for topic ID: 7
2025-06-24 17:13:05.394 +08:00 [DBG] info: 2025/6/24 17:13:05.394 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-24 17:13:05.416 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-24 17:18:55.548 +08:00 [INF] Application Shutting Down
2025-06-24 17:18:59.875 +08:00 [INF] Application Starting Up
2025-06-24 17:19:00.978 +08:00 [DBG] warn: 2025/6/24 17:19:00.977 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-24 17:19:01.149 +08:00 [DBG] info: 2025/6/24 17:19:01.149 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-24 17:19:01.155 +08:00 [INF] TopicService initialized and database ensured.
2025-06-24 17:19:01.335 +08:00 [INF] Initializing SemanticKernelService...
2025-06-24 17:19:01.355 +08:00 [INF] SemanticKernelService initialized.
2025-06-24 17:19:08.616 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-24 17:19:08.622 +08:00 [INF] Getting topics for user: llk
2025-06-24 17:19:09.130 +08:00 [DBG] info: 2025/6/24 17:19:09.130 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (6ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-24 17:19:09.199 +08:00 [INF] Getting messages for topic ID: 7
2025-06-24 17:19:09.217 +08:00 [DBG] info: 2025/6/24 17:19:09.217 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-24 17:19:09.242 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-24 17:26:00.991 +08:00 [INF] Application Shutting Down
2025-06-24 17:27:39.190 +08:00 [INF] Application Starting Up
2025-06-24 17:27:40.289 +08:00 [DBG] warn: 2025/6/24 17:27:40.289 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-24 17:27:40.461 +08:00 [DBG] info: 2025/6/24 17:27:40.461 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-24 17:27:40.466 +08:00 [INF] TopicService initialized and database ensured.
2025-06-24 17:27:40.640 +08:00 [INF] Initializing SemanticKernelService...
2025-06-24 17:27:40.659 +08:00 [INF] SemanticKernelService initialized.
2025-06-24 17:27:47.827 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-24 17:27:47.833 +08:00 [INF] Getting topics for user: llk
2025-06-24 17:27:48.347 +08:00 [DBG] info: 2025/6/24 17:27:48.347 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (6ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-24 17:27:48.409 +08:00 [INF] Getting messages for topic ID: 7
2025-06-24 17:27:48.425 +08:00 [DBG] info: 2025/6/24 17:27:48.425 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-24 17:27:48.448 +08:00 [INF] Setting conversation history. Message count: 4
