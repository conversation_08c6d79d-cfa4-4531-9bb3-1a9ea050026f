2025-07-15 11:14:11.255 +08:00 [DBG] Hosting starting
2025-07-15 11:14:11.315 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 11:14:11.322 +08:00 [INF] Hosting environment: Production
2025-07-15 11:14:11.324 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai
2025-07-15 11:14:11.326 +08:00 [DBG] Hosting started
2025-07-15 11:14:11.327 +08:00 [INF] Application Starting Up
2025-07-15 11:14:12.324 +08:00 [DBG] warn: 2025/7/15 11:14:12.324 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-15 11:14:12.484 +08:00 [DBG] info: 2025/7/15 11:14:12.484 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-15 11:14:12.489 +08:00 [INF] TopicService initialized and database ensured.
2025-07-15 11:14:12.656 +08:00 [INF] Initializing SemanticKernelService...
2025-07-15 11:14:12.675 +08:00 [INF] SemanticKernelService initialized.
2025-07-15 11:14:16.680 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-15 11:14:20.661 +08:00 [ERR] 在 Kernel 工厂方法中添加共享 MCP 插件时出错
System.IO.IOException: MCP server process exited unexpectedly (exit code: 1)
Server's stderr tail:
[INFO] Using SQLite database at path: uploads/llk/temp.db
[INFO] Initializing sqlite database...
[INFO] Database path: uploads/llk/temp.db
[INFO] Opening SQLite database at: uploads/llk/temp.db
[ERROR] SQLite connection error: SQLITE_CANTOPEN: unable to open database file
[ERROR] Failed to initialize: Error: Failed to initialize database: SQLITE_CANTOPEN: unable to open database file
    at initDatabase (file:///C:/Users/<USER>/AppData/Roaming/npm/node_modules/@executeautomation/database-server/dist/src/db/index.js:21:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async runServer (file:///C:/Users/<USER>/AppData/Roaming/npm/node_modules/@executeautomation/database-server/dist/src/index.js:219:9)
   at System.Threading.Channels.AsyncOperation`1.GetResult(Int16 token)
   at System.Threading.Channels.ChannelReader`1.ReadAllAsync(CancellationToken cancellationToken)+MoveNext()
   at System.Threading.Channels.ChannelReader`1.ReadAllAsync(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at ModelContextProtocol.McpSession.ProcessMessagesAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.McpSession.ProcessMessagesAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.McpEndpoint.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.Client.McpClient.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.Client.McpClient.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.McpEndpoint.DisposeAsync()
   at ModelContextProtocol.Client.McpClient.ConnectAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.Client.McpClientFactory.CreateAsync(IClientTransport clientTransport, McpClientOptions clientOptions, ILoggerFactory loggerFactory, CancellationToken cancellationToken)
   at ModelContextProtocol.Client.McpClientFactory.CreateAsync(IClientTransport clientTransport, McpClientOptions clientOptions, ILoggerFactory loggerFactory, CancellationToken cancellationToken)
   at ModelContextProtocol.SemanticKernel.Extensions.KernelExtensions.GetStdioClientAsync(String serverName, ModelContextProtocolSemanticKernelStdioOptions options, CancellationToken cancellationToken)
   at ModelContextProtocol.SemanticKernel.Extensions.KernelExtensions.AddMcpFunctionsFromStdioServerAsync(KernelPluginCollection plugins, ModelContextProtocolSemanticKernelStdioOptions options, CancellationToken cancellationToken)
   at Yidev.LocalAI.Services.SemanticKernelService.InitializeAsync() in D:\yidev\Source\yidev.localai\Services\SemanticKernelService.cs:line 65
2025-07-15 11:15:20.321 +08:00 [DBG] Hosting starting
2025-07-15 11:15:20.382 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 11:15:20.388 +08:00 [INF] Hosting environment: Production
2025-07-15 11:15:20.390 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai
2025-07-15 11:15:20.391 +08:00 [DBG] Hosting started
2025-07-15 11:15:20.393 +08:00 [INF] Application Starting Up
2025-07-15 11:15:21.377 +08:00 [DBG] warn: 2025/7/15 11:15:21.377 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-15 11:15:21.541 +08:00 [DBG] info: 2025/7/15 11:15:21.541 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-15 11:15:21.546 +08:00 [INF] TopicService initialized and database ensured.
2025-07-15 11:15:21.716 +08:00 [INF] Initializing SemanticKernelService...
2025-07-15 11:15:21.736 +08:00 [INF] SemanticKernelService initialized.
2025-07-15 11:15:25.430 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-15 11:15:29.430 +08:00 [ERR] 在 Kernel 工厂方法中添加共享 MCP 插件时出错
System.IO.IOException: MCP server process exited unexpectedly (exit code: 1)
Server's stderr tail:
[INFO] Using SQLite database at path: uploads/llk/temp.db
[INFO] Initializing sqlite database...
[INFO] Database path: uploads/llk/temp.db
[INFO] Opening SQLite database at: uploads/llk/temp.db
[ERROR] SQLite connection error: SQLITE_CANTOPEN: unable to open database file
[ERROR] Failed to initialize: Error: Failed to initialize database: SQLITE_CANTOPEN: unable to open database file
    at initDatabase (file:///C:/Users/<USER>/AppData/Roaming/npm/node_modules/@executeautomation/database-server/dist/src/db/index.js:21:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async runServer (file:///C:/Users/<USER>/AppData/Roaming/npm/node_modules/@executeautomation/database-server/dist/src/index.js:219:9)
   at System.Threading.Channels.AsyncOperation`1.GetResult(Int16 token)
   at System.Threading.Channels.ChannelReader`1.ReadAllAsync(CancellationToken cancellationToken)+MoveNext()
   at System.Threading.Channels.ChannelReader`1.ReadAllAsync(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at ModelContextProtocol.McpSession.ProcessMessagesAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.McpSession.ProcessMessagesAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.McpEndpoint.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.Client.McpClient.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.Client.McpClient.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.McpEndpoint.DisposeAsync()
   at ModelContextProtocol.Client.McpClient.ConnectAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.Client.McpClientFactory.CreateAsync(IClientTransport clientTransport, McpClientOptions clientOptions, ILoggerFactory loggerFactory, CancellationToken cancellationToken)
   at ModelContextProtocol.Client.McpClientFactory.CreateAsync(IClientTransport clientTransport, McpClientOptions clientOptions, ILoggerFactory loggerFactory, CancellationToken cancellationToken)
   at ModelContextProtocol.SemanticKernel.Extensions.KernelExtensions.GetStdioClientAsync(String serverName, ModelContextProtocolSemanticKernelStdioOptions options, CancellationToken cancellationToken)
   at ModelContextProtocol.SemanticKernel.Extensions.KernelExtensions.AddMcpFunctionsFromStdioServerAsync(KernelPluginCollection plugins, ModelContextProtocolSemanticKernelStdioOptions options, CancellationToken cancellationToken)
   at Yidev.LocalAI.Services.SemanticKernelService.InitializeAsync() in D:\yidev\Source\yidev.localai\Services\SemanticKernelService.cs:line 65
2025-07-15 11:15:29.450 +08:00 [WRN] MCP 插件初始化失败，应用程序将在没有 MCP 插件的情况下继续运行
2025-07-15 11:15:29.455 +08:00 [INF] Getting topics for user: llk
2025-07-15 11:15:29.957 +08:00 [DBG] info: 2025/7/15 11:15:29.957 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-15 11:15:30.016 +08:00 [INF] Getting messages for topic ID: 1
2025-07-15 11:15:30.031 +08:00 [DBG] info: 2025/7/15 11:15:30.031 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='1'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-15 11:15:30.057 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-15 11:15:30.058 +08:00 [INF] Setting conversation history. Message count: 1
2025-07-15 11:16:56.081 +08:00 [DBG] Hosting starting
2025-07-15 11:16:56.145 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 11:16:56.152 +08:00 [INF] Hosting environment: Production
2025-07-15 11:16:56.154 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai
2025-07-15 11:16:56.155 +08:00 [DBG] Hosting started
2025-07-15 11:16:56.157 +08:00 [INF] Application Starting Up
2025-07-15 11:16:57.150 +08:00 [DBG] warn: 2025/7/15 11:16:57.150 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-15 11:16:57.312 +08:00 [DBG] info: 2025/7/15 11:16:57.312 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-15 11:16:57.318 +08:00 [INF] TopicService initialized and database ensured.
2025-07-15 11:16:57.494 +08:00 [INF] Initializing SemanticKernelService...
2025-07-15 11:16:57.513 +08:00 [INF] SemanticKernelService initialized.
2025-07-15 11:17:00.963 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-15 11:17:05.628 +08:00 [ERR] 在 Kernel 工厂方法中添加共享 MCP 插件时出错
System.IO.IOException: MCP server process exited unexpectedly (exit code: 1)
Server's stderr tail:
[INFO] Using SQLite database at path: uploads/llk/temp.db
[INFO] Initializing sqlite database...
[INFO] Database path: uploads/llk/temp.db
[INFO] Opening SQLite database at: uploads/llk/temp.db
[ERROR] SQLite connection error: SQLITE_CANTOPEN: unable to open database file
[ERROR] Failed to initialize: Error: Failed to initialize database: SQLITE_CANTOPEN: unable to open database file
    at initDatabase (file:///C:/Users/<USER>/AppData/Roaming/npm/node_modules/@executeautomation/database-server/dist/src/db/index.js:21:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async runServer (file:///C:/Users/<USER>/AppData/Roaming/npm/node_modules/@executeautomation/database-server/dist/src/index.js:219:9)
   at System.Threading.Channels.AsyncOperation`1.GetResult(Int16 token)
   at System.Threading.Channels.ChannelReader`1.ReadAllAsync(CancellationToken cancellationToken)+MoveNext()
   at System.Threading.Channels.ChannelReader`1.ReadAllAsync(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at ModelContextProtocol.McpSession.ProcessMessagesAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.McpSession.ProcessMessagesAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.McpEndpoint.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.Client.McpClient.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.Client.McpClient.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.McpEndpoint.DisposeAsync()
   at ModelContextProtocol.Client.McpClient.ConnectAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.Client.McpClientFactory.CreateAsync(IClientTransport clientTransport, McpClientOptions clientOptions, ILoggerFactory loggerFactory, CancellationToken cancellationToken)
   at ModelContextProtocol.Client.McpClientFactory.CreateAsync(IClientTransport clientTransport, McpClientOptions clientOptions, ILoggerFactory loggerFactory, CancellationToken cancellationToken)
   at ModelContextProtocol.SemanticKernel.Extensions.KernelExtensions.GetStdioClientAsync(String serverName, ModelContextProtocolSemanticKernelStdioOptions options, CancellationToken cancellationToken)
   at ModelContextProtocol.SemanticKernel.Extensions.KernelExtensions.AddMcpFunctionsFromStdioServerAsync(KernelPluginCollection plugins, ModelContextProtocolSemanticKernelStdioOptions options, CancellationToken cancellationToken)
   at Yidev.LocalAI.Services.SemanticKernelService.InitializeAsync() in D:\yidev\Source\yidev.localai\Services\SemanticKernelService.cs:line 65
2025-07-15 11:17:05.650 +08:00 [WRN] MCP 插件初始化失败，应用程序将在没有 MCP 插件的情况下继续运行
2025-07-15 11:17:05.655 +08:00 [INF] Getting topics for user: llk
2025-07-15 11:17:06.162 +08:00 [DBG] info: 2025/7/15 11:17:06.162 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (6ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-15 11:17:06.227 +08:00 [INF] Getting messages for topic ID: 1
2025-07-15 11:17:06.243 +08:00 [DBG] info: 2025/7/15 11:17:06.243 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='1'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-15 11:17:06.270 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-15 11:17:06.271 +08:00 [INF] Setting conversation history. Message count: 1
2025-07-15 11:18:05.728 +08:00 [DBG] Hosting starting
2025-07-15 11:18:05.788 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 11:18:05.794 +08:00 [INF] Hosting environment: Production
2025-07-15 11:18:05.796 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai
2025-07-15 11:18:05.797 +08:00 [DBG] Hosting started
2025-07-15 11:18:05.799 +08:00 [INF] Application Starting Up
2025-07-15 11:18:06.780 +08:00 [DBG] warn: 2025/7/15 11:18:06.780 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-15 11:18:06.948 +08:00 [DBG] info: 2025/7/15 11:18:06.948 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-15 11:18:06.953 +08:00 [INF] TopicService initialized and database ensured.
2025-07-15 11:18:07.125 +08:00 [INF] Initializing SemanticKernelService...
2025-07-15 11:18:07.144 +08:00 [INF] SemanticKernelService initialized.
2025-07-15 11:18:12.065 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-15 11:18:16.086 +08:00 [ERR] 在 Kernel 工厂方法中添加共享 MCP 插件时出错
System.IO.IOException: MCP server process exited unexpectedly (exit code: 1)
Server's stderr tail:
[INFO] Using SQLite database at path: uploads/llk/temp.db
[INFO] Initializing sqlite database...
[INFO] Database path: uploads/llk/temp.db
[INFO] Opening SQLite database at: uploads/llk/temp.db
[ERROR] SQLite connection error: SQLITE_CANTOPEN: unable to open database file
[ERROR] Failed to initialize: Error: Failed to initialize database: SQLITE_CANTOPEN: unable to open database file
    at initDatabase (file:///C:/Users/<USER>/AppData/Roaming/npm/node_modules/@executeautomation/database-server/dist/src/db/index.js:21:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async runServer (file:///C:/Users/<USER>/AppData/Roaming/npm/node_modules/@executeautomation/database-server/dist/src/index.js:219:9)
   at System.Threading.Channels.AsyncOperation`1.GetResult(Int16 token)
   at System.Threading.Channels.ChannelReader`1.ReadAllAsync(CancellationToken cancellationToken)+MoveNext()
   at System.Threading.Channels.ChannelReader`1.ReadAllAsync(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at ModelContextProtocol.McpSession.ProcessMessagesAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.McpSession.ProcessMessagesAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.McpEndpoint.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.Client.McpClient.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.Client.McpClient.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.McpEndpoint.DisposeAsync()
   at ModelContextProtocol.Client.McpClient.ConnectAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.Client.McpClientFactory.CreateAsync(IClientTransport clientTransport, McpClientOptions clientOptions, ILoggerFactory loggerFactory, CancellationToken cancellationToken)
   at ModelContextProtocol.Client.McpClientFactory.CreateAsync(IClientTransport clientTransport, McpClientOptions clientOptions, ILoggerFactory loggerFactory, CancellationToken cancellationToken)
   at ModelContextProtocol.SemanticKernel.Extensions.KernelExtensions.GetStdioClientAsync(String serverName, ModelContextProtocolSemanticKernelStdioOptions options, CancellationToken cancellationToken)
   at ModelContextProtocol.SemanticKernel.Extensions.KernelExtensions.AddMcpFunctionsFromStdioServerAsync(KernelPluginCollection plugins, ModelContextProtocolSemanticKernelStdioOptions options, CancellationToken cancellationToken)
   at Yidev.LocalAI.Services.SemanticKernelService.InitializeAsync() in D:\yidev\Source\yidev.localai\Services\SemanticKernelService.cs:line 65
2025-07-15 11:18:16.107 +08:00 [WRN] MCP 插件初始化失败，应用程序将在没有 MCP 插件的情况下继续运行
2025-07-15 11:18:16.112 +08:00 [INF] Getting topics for user: llk
2025-07-15 11:18:16.627 +08:00 [DBG] info: 2025/7/15 11:18:16.627 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-15 11:18:16.687 +08:00 [INF] Getting messages for topic ID: 1
2025-07-15 11:18:16.703 +08:00 [DBG] info: 2025/7/15 11:18:16.703 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='1'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-15 11:18:16.729 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-15 11:18:16.730 +08:00 [INF] Setting conversation history. Message count: 1
2025-07-15 11:19:37.655 +08:00 [INF] Application Shutting Down
2025-07-15 11:19:37.659 +08:00 [DBG] Hosting stopping
2025-07-15 11:19:37.660 +08:00 [INF] Application is shutting down...
2025-07-15 11:19:37.662 +08:00 [DBG] Hosting stopped
2025-07-15 15:30:21.902 +08:00 [DBG] Hosting starting
2025-07-15 15:30:21.964 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 15:30:21.970 +08:00 [INF] Hosting environment: Production
2025-07-15 15:30:21.973 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai
2025-07-15 15:30:21.974 +08:00 [DBG] Hosting started
2025-07-15 15:30:21.976 +08:00 [INF] Application Starting Up
2025-07-15 15:30:22.961 +08:00 [DBG] warn: 2025/7/15 15:30:22.961 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-15 15:30:23.122 +08:00 [DBG] info: 2025/7/15 15:30:23.122 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-15 15:30:23.128 +08:00 [INF] TopicService initialized and database ensured.
2025-07-15 15:30:23.306 +08:00 [INF] Initializing SemanticKernelService...
2025-07-15 15:30:23.326 +08:00 [INF] SemanticKernelService initialized.
2025-07-15 15:30:27.870 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-15 15:30:31.633 +08:00 [ERR] 在 Kernel 工厂方法中添加共享 MCP 插件时出错
System.IO.IOException: MCP server process exited unexpectedly (exit code: 1)
Server's stderr tail:
[INFO] Using SQLite database at path: uploads/llk/temp.db
[INFO] Initializing sqlite database...
[INFO] Database path: uploads/llk/temp.db
[INFO] Opening SQLite database at: uploads/llk/temp.db
[ERROR] SQLite connection error: SQLITE_CANTOPEN: unable to open database file
[ERROR] Failed to initialize: Error: Failed to initialize database: SQLITE_CANTOPEN: unable to open database file
    at initDatabase (file:///C:/Users/<USER>/AppData/Roaming/npm/node_modules/@executeautomation/database-server/dist/src/db/index.js:21:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async runServer (file:///C:/Users/<USER>/AppData/Roaming/npm/node_modules/@executeautomation/database-server/dist/src/index.js:219:9)
   at System.Threading.Channels.AsyncOperation`1.GetResult(Int16 token)
   at System.Threading.Channels.ChannelReader`1.ReadAllAsync(CancellationToken cancellationToken)+MoveNext()
   at System.Threading.Channels.ChannelReader`1.ReadAllAsync(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at ModelContextProtocol.McpSession.ProcessMessagesAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.McpSession.ProcessMessagesAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.McpEndpoint.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.Client.McpClient.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.Client.McpClient.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.McpEndpoint.DisposeAsync()
   at ModelContextProtocol.Client.McpClient.ConnectAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.Client.McpClientFactory.CreateAsync(IClientTransport clientTransport, McpClientOptions clientOptions, ILoggerFactory loggerFactory, CancellationToken cancellationToken)
   at ModelContextProtocol.Client.McpClientFactory.CreateAsync(IClientTransport clientTransport, McpClientOptions clientOptions, ILoggerFactory loggerFactory, CancellationToken cancellationToken)
   at ModelContextProtocol.SemanticKernel.Extensions.KernelExtensions.GetStdioClientAsync(String serverName, ModelContextProtocolSemanticKernelStdioOptions options, CancellationToken cancellationToken)
   at ModelContextProtocol.SemanticKernel.Extensions.KernelExtensions.AddMcpFunctionsFromStdioServerAsync(KernelPluginCollection plugins, ModelContextProtocolSemanticKernelStdioOptions options, CancellationToken cancellationToken)
   at Yidev.LocalAI.Services.SemanticKernelService.InitializeAsync() in D:\yidev\Source\yidev.localai\Services\SemanticKernelService.cs:line 65
2025-07-15 15:30:31.655 +08:00 [WRN] MCP 插件初始化失败，应用程序将在没有 MCP 插件的情况下继续运行
2025-07-15 15:30:31.660 +08:00 [INF] Getting topics for user: llk
2025-07-15 15:30:32.175 +08:00 [DBG] info: 2025/7/15 15:30:32.175 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-15 15:30:32.235 +08:00 [INF] Getting messages for topic ID: 1
2025-07-15 15:30:32.250 +08:00 [DBG] info: 2025/7/15 15:30:32.250 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='1'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-15 15:30:32.277 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-15 15:30:32.278 +08:00 [INF] Setting conversation history. Message count: 1
2025-07-15 15:30:55.672 +08:00 [INF] Application Shutting Down
2025-07-15 15:30:55.676 +08:00 [DBG] Hosting stopping
2025-07-15 15:30:55.677 +08:00 [INF] Application is shutting down...
2025-07-15 15:30:55.679 +08:00 [DBG] Hosting stopped
2025-07-15 15:42:31.119 +08:00 [DBG] Hosting starting
2025-07-15 15:42:31.180 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 15:42:31.187 +08:00 [INF] Hosting environment: Production
2025-07-15 15:42:31.189 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai
2025-07-15 15:42:31.190 +08:00 [DBG] Hosting started
2025-07-15 15:42:31.191 +08:00 [INF] Application Starting Up
2025-07-15 15:42:32.184 +08:00 [DBG] warn: 2025/7/15 15:42:32.183 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-15 15:42:32.341 +08:00 [DBG] info: 2025/7/15 15:42:32.341 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-15 15:42:32.347 +08:00 [INF] TopicService initialized and database ensured.
2025-07-15 15:42:32.516 +08:00 [INF] Initializing SemanticKernelService...
2025-07-15 15:42:32.535 +08:00 [INF] SemanticKernelService initialized.
2025-07-15 15:42:37.151 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-15 15:42:41.601 +08:00 [ERR] 在 Kernel 工厂方法中添加共享 MCP 插件时出错
System.IO.IOException: MCP server process exited unexpectedly (exit code: 1)
Server's stderr tail:
[INFO] Using SQLite database at path: uploads/llk/temp.db
[INFO] Initializing sqlite database...
[INFO] Database path: uploads/llk/temp.db
[INFO] Opening SQLite database at: uploads/llk/temp.db
[ERROR] SQLite connection error: SQLITE_CANTOPEN: unable to open database file
[ERROR] Failed to initialize: Error: Failed to initialize database: SQLITE_CANTOPEN: unable to open database file
    at initDatabase (file:///C:/Users/<USER>/AppData/Roaming/npm/node_modules/@executeautomation/database-server/dist/src/db/index.js:21:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async runServer (file:///C:/Users/<USER>/AppData/Roaming/npm/node_modules/@executeautomation/database-server/dist/src/index.js:219:9)
   at System.Threading.Channels.AsyncOperation`1.GetResult(Int16 token)
   at System.Threading.Channels.ChannelReader`1.ReadAllAsync(CancellationToken cancellationToken)+MoveNext()
   at System.Threading.Channels.ChannelReader`1.ReadAllAsync(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at ModelContextProtocol.McpSession.ProcessMessagesAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.McpSession.ProcessMessagesAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.McpEndpoint.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.Client.McpClient.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.Client.McpClient.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.McpEndpoint.DisposeAsync()
   at ModelContextProtocol.Client.McpClient.ConnectAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.Client.McpClientFactory.CreateAsync(IClientTransport clientTransport, McpClientOptions clientOptions, ILoggerFactory loggerFactory, CancellationToken cancellationToken)
   at ModelContextProtocol.Client.McpClientFactory.CreateAsync(IClientTransport clientTransport, McpClientOptions clientOptions, ILoggerFactory loggerFactory, CancellationToken cancellationToken)
   at ModelContextProtocol.SemanticKernel.Extensions.KernelExtensions.GetStdioClientAsync(String serverName, ModelContextProtocolSemanticKernelStdioOptions options, CancellationToken cancellationToken)
   at ModelContextProtocol.SemanticKernel.Extensions.KernelExtensions.AddMcpFunctionsFromStdioServerAsync(KernelPluginCollection plugins, ModelContextProtocolSemanticKernelStdioOptions options, CancellationToken cancellationToken)
   at Yidev.LocalAI.Services.SemanticKernelService.InitializeAsync() in D:\yidev\Source\yidev.localai\Services\SemanticKernelService.cs:line 65
2025-07-15 15:42:41.622 +08:00 [WRN] MCP 插件初始化失败，应用程序将在没有 MCP 插件的情况下继续运行
2025-07-15 15:42:41.627 +08:00 [INF] Getting topics for user: llk
2025-07-15 15:42:42.130 +08:00 [DBG] info: 2025/7/15 15:42:42.130 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-15 15:42:42.190 +08:00 [INF] Getting messages for topic ID: 1
2025-07-15 15:42:42.205 +08:00 [DBG] info: 2025/7/15 15:42:42.205 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='1'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-15 15:42:42.232 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-15 15:42:42.233 +08:00 [INF] Setting conversation history. Message count: 1
2025-07-15 15:42:58.857 +08:00 [INF] Application Shutting Down
2025-07-15 15:42:58.861 +08:00 [DBG] Hosting stopping
2025-07-15 15:42:58.863 +08:00 [INF] Application is shutting down...
2025-07-15 15:42:58.864 +08:00 [DBG] Hosting stopped
2025-07-15 15:52:18.891 +08:00 [DBG] Hosting starting
2025-07-15 15:52:18.954 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 15:52:18.960 +08:00 [INF] Hosting environment: Production
2025-07-15 15:52:18.963 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai
2025-07-15 15:52:18.964 +08:00 [DBG] Hosting started
2025-07-15 15:52:18.965 +08:00 [INF] Application Starting Up
2025-07-15 15:52:19.972 +08:00 [DBG] warn: 2025/7/15 15:52:19.972 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-15 15:52:20.135 +08:00 [DBG] info: 2025/7/15 15:52:20.134 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-15 15:52:20.140 +08:00 [INF] TopicService initialized and database ensured.
2025-07-15 15:52:20.315 +08:00 [INF] Initializing SemanticKernelService...
2025-07-15 15:52:20.335 +08:00 [INF] SemanticKernelService initialized.
2025-07-15 15:52:24.642 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-15 15:52:31.639 +08:00 [ERR] 在 Kernel 工厂方法中添加共享 MCP 插件时出错
System.IO.IOException: MCP server process exited unexpectedly (exit code: 1)
Server's stderr tail:
[INFO] Using SQLite database at path: uploads/llk/temp.db
[INFO] Initializing sqlite database...
[INFO] Database path: uploads/llk/temp.db
[INFO] Opening SQLite database at: uploads/llk/temp.db
[ERROR] SQLite connection error: SQLITE_CANTOPEN: unable to open database file
[ERROR] Failed to initialize: Error: Failed to initialize database: SQLITE_CANTOPEN: unable to open database file
    at initDatabase (file:///C:/Users/<USER>/AppData/Roaming/npm/node_modules/@executeautomation/database-server/dist/src/db/index.js:21:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async runServer (file:///C:/Users/<USER>/AppData/Roaming/npm/node_modules/@executeautomation/database-server/dist/src/index.js:219:9)
   at System.Threading.Channels.AsyncOperation`1.GetResult(Int16 token)
   at System.Threading.Channels.ChannelReader`1.ReadAllAsync(CancellationToken cancellationToken)+MoveNext()
   at System.Threading.Channels.ChannelReader`1.ReadAllAsync(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at ModelContextProtocol.McpSession.ProcessMessagesAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.McpSession.ProcessMessagesAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.McpEndpoint.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.Client.McpClient.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.Client.McpClient.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.McpEndpoint.DisposeAsync()
   at ModelContextProtocol.Client.McpClient.ConnectAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.Client.McpClientFactory.CreateAsync(IClientTransport clientTransport, McpClientOptions clientOptions, ILoggerFactory loggerFactory, CancellationToken cancellationToken)
   at ModelContextProtocol.Client.McpClientFactory.CreateAsync(IClientTransport clientTransport, McpClientOptions clientOptions, ILoggerFactory loggerFactory, CancellationToken cancellationToken)
   at ModelContextProtocol.SemanticKernel.Extensions.KernelExtensions.GetStdioClientAsync(String serverName, ModelContextProtocolSemanticKernelStdioOptions options, CancellationToken cancellationToken)
   at ModelContextProtocol.SemanticKernel.Extensions.KernelExtensions.AddMcpFunctionsFromStdioServerAsync(KernelPluginCollection plugins, ModelContextProtocolSemanticKernelStdioOptions options, CancellationToken cancellationToken)
   at Yidev.LocalAI.Services.SemanticKernelService.InitializeAsync() in D:\yidev\Source\yidev.localai\Services\SemanticKernelService.cs:line 65
2025-07-15 15:52:31.661 +08:00 [WRN] MCP 插件初始化失败，应用程序将在没有 MCP 插件的情况下继续运行
2025-07-15 15:52:31.666 +08:00 [INF] Getting topics for user: llk
2025-07-15 15:52:32.180 +08:00 [DBG] info: 2025/7/15 15:52:32.180 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-15 15:52:32.240 +08:00 [INF] Getting messages for topic ID: 1
2025-07-15 15:52:32.256 +08:00 [DBG] info: 2025/7/15 15:52:32.256 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='1'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-15 15:52:32.283 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-15 15:52:32.284 +08:00 [INF] Setting conversation history. Message count: 1
2025-07-15 15:53:03.548 +08:00 [INF] Application Shutting Down
2025-07-15 15:53:03.552 +08:00 [DBG] Hosting stopping
2025-07-15 15:53:03.554 +08:00 [INF] Application is shutting down...
2025-07-15 15:53:03.555 +08:00 [DBG] Hosting stopped
2025-07-15 16:08:31.776 +08:00 [DBG] Hosting starting
2025-07-15 16:08:31.839 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 16:08:31.845 +08:00 [INF] Hosting environment: Production
2025-07-15 16:08:31.847 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai
2025-07-15 16:08:31.849 +08:00 [DBG] Hosting started
2025-07-15 16:08:31.850 +08:00 [INF] Application Starting Up
2025-07-15 16:08:32.852 +08:00 [DBG] warn: 2025/7/15 16:08:32.852 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-15 16:08:33.011 +08:00 [DBG] info: 2025/7/15 16:08:33.011 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-15 16:08:33.016 +08:00 [INF] TopicService initialized and database ensured.
2025-07-15 16:08:33.188 +08:00 [INF] Initializing SemanticKernelService...
2025-07-15 16:08:33.209 +08:00 [INF] SemanticKernelService initialized.
2025-07-15 16:08:36.492 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-15 16:08:41.231 +08:00 [ERR] 在 Kernel 工厂方法中添加共享 MCP 插件时出错
System.IO.IOException: MCP server process exited unexpectedly (exit code: 1)
Server's stderr tail:
[INFO] Using SQLite database at path: uploads/llk/temp.db
[INFO] Initializing sqlite database...
[INFO] Database path: uploads/llk/temp.db
[INFO] Opening SQLite database at: uploads/llk/temp.db
[ERROR] SQLite connection error: SQLITE_CANTOPEN: unable to open database file
[ERROR] Failed to initialize: Error: Failed to initialize database: SQLITE_CANTOPEN: unable to open database file
    at initDatabase (file:///C:/Users/<USER>/AppData/Roaming/npm/node_modules/@executeautomation/database-server/dist/src/db/index.js:21:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
    at async runServer (file:///C:/Users/<USER>/AppData/Roaming/npm/node_modules/@executeautomation/database-server/dist/src/index.js:219:9)
   at System.Threading.Channels.AsyncOperation`1.GetResult(Int16 token)
   at System.Threading.Channels.ChannelReader`1.ReadAllAsync(CancellationToken cancellationToken)+MoveNext()
   at System.Threading.Channels.ChannelReader`1.ReadAllAsync(CancellationToken cancellationToken)+System.Threading.Tasks.Sources.IValueTaskSource<System.Boolean>.GetResult()
   at ModelContextProtocol.McpSession.ProcessMessagesAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.McpSession.ProcessMessagesAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.McpEndpoint.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.Client.McpClient.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.Client.McpClient.DisposeUnsynchronizedAsync()
   at ModelContextProtocol.McpEndpoint.DisposeAsync()
   at ModelContextProtocol.Client.McpClient.ConnectAsync(CancellationToken cancellationToken)
   at ModelContextProtocol.Client.McpClientFactory.CreateAsync(IClientTransport clientTransport, McpClientOptions clientOptions, ILoggerFactory loggerFactory, CancellationToken cancellationToken)
   at ModelContextProtocol.Client.McpClientFactory.CreateAsync(IClientTransport clientTransport, McpClientOptions clientOptions, ILoggerFactory loggerFactory, CancellationToken cancellationToken)
   at ModelContextProtocol.SemanticKernel.Extensions.KernelExtensions.GetStdioClientAsync(String serverName, ModelContextProtocolSemanticKernelStdioOptions options, CancellationToken cancellationToken)
   at ModelContextProtocol.SemanticKernel.Extensions.KernelExtensions.AddMcpFunctionsFromStdioServerAsync(KernelPluginCollection plugins, ModelContextProtocolSemanticKernelStdioOptions options, CancellationToken cancellationToken)
   at Yidev.LocalAI.Services.SemanticKernelService.InitializeAsync() in D:\yidev\Source\yidev.localai\Services\SemanticKernelService.cs:line 65
2025-07-15 16:08:41.253 +08:00 [WRN] MCP 插件初始化失败，应用程序将在没有 MCP 插件的情况下继续运行
2025-07-15 16:08:41.259 +08:00 [INF] Getting topics for user: llk
2025-07-15 16:08:41.800 +08:00 [DBG] info: 2025/7/15 16:08:41.800 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (6ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-15 16:08:41.865 +08:00 [INF] Getting messages for topic ID: 1
2025-07-15 16:08:41.881 +08:00 [DBG] info: 2025/7/15 16:08:41.881 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='1'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-15 16:08:41.910 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-15 16:08:41.911 +08:00 [INF] Setting conversation history. Message count: 1
2025-07-15 16:08:54.574 +08:00 [INF] Application Shutting Down
2025-07-15 16:08:54.578 +08:00 [DBG] Hosting stopping
2025-07-15 16:08:54.580 +08:00 [INF] Application is shutting down...
2025-07-15 16:08:54.582 +08:00 [DBG] Hosting stopped
