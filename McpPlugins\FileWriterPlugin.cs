﻿using System;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Security;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;

namespace Yidev.LocalAI.McpPlugins // 确保命名空间正确
{
    public class FileWriterPlugin
    {
        private readonly ILogger<FileWriterPlugin> _logger;
        private readonly string _baseUploadDirectory;
        private readonly long _maxWriteSizeBytes = 10 * 1024 * 1024; // 限制写入文件大小为 10MB

        public FileWriterPlugin(
            ILogger<FileWriterPlugin> logger)
        {
            _logger = logger;
            _baseUploadDirectory = Path.Combine(AppContext.BaseDirectory, "uploads");

            // 确保基础上传目录存在
            if (!Directory.Exists(_baseUploadDirectory))
            {
                try
                {
                    Directory.CreateDirectory(_baseUploadDirectory);
                    _logger.LogInformation("基础上传目录 {BaseUploadDirectory} 已创建。", _baseUploadDirectory);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "创建基础上传目录 {BaseUploadDirectory} 失败。插件可能无法正常工作。", _baseUploadDirectory);
                    // 根据应用需求，这里可能需要抛出异常，因为插件可能无法正常工作
                }
            }
        }

        // 辅助方法：获取当前用户的上传目录（需要 HttpContext）
        private string GetUserUploadDirectory()
        {
            var viewModel = ViewModelLocator.MainViewModelInstance; // Use shared instance

            // 对用户名进行清理，以创建安全的目录名
            var sanitizedUserName = string.Join("_", viewModel.Username.Split(Path.GetInvalidFileNameChars()));
            if (string.IsNullOrWhiteSpace(sanitizedUserName))
            {
                // 如果清理后用户名为空, 可以选择抛出异常或使用默认名称
                _logger.LogWarning("清理后的用户名为空 (原始: {OriginalUserName}), 无法创建用户目录。", viewModel.Username);
                throw new InvalidOperationException("用户名无效，无法创建用户目录。");
            }

            var userDirectory = Path.Combine(_baseUploadDirectory, sanitizedUserName);

            // 确保用户目录存在 (写入时必须创建)
            if (!Directory.Exists(userDirectory))
            {
                _logger.LogInformation("用户目录 {UserDirectory} 不存在，正在创建。", userDirectory);
                try
                {
                    Directory.CreateDirectory(userDirectory);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "创建用户目录 {UserDirectory} 失败。", userDirectory);
                    // 向上抛出异常，让调用者知道目录创建失败
                    throw new IOException($"创建用户目录 '{userDirectory}' 失败。", ex);
                }
            }
            return userDirectory;
        }

        [KernelFunction,
         Description("将指定的文本内容写入到用户上传目录下的一个文件。如果文件已存在，则会覆盖它。如果目录不存在，会尝试创建。此工具用于保存文本数据，如笔记、代码、配置等。")]
        public async Task<string> WriteTextFileAsync(
            [Description("要写入的文件的相对路径（在用户上传目录内）。例如：'notes/meeting_summary.txt' 或 'config.json'。不允许使用 '..' 或绝对路径来尝试写入到用户目录之外。")] string relativeFilePath,
            [Description("要写入文件的文本内容。")] string content
        )
        {
            string userDirectory;
            try
            {
                userDirectory = GetUserUploadDirectory();
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError(ex, "获取用户目录失败，无法写入文件。");
                return $"错误：无法确定用户目录。{ex.Message}";
            }
            catch (IOException ex) // GetUserUploadDirectory 中创建目录可能抛出
            {
                _logger.LogError(ex, "获取或创建用户目录时发生IO错误。");
                return $"错误：处理用户目录时发生错误。{ex.Message}";
            }
            catch (Exception ex) // 捕获 GetUserUploadDirectory 其他潜在错误
            {
                _logger.LogError(ex, "获取用户目录时发生意外错误。");
                return $"错误：获取用户目录时发生意外错误。 {ex.Message}";
            }

            _logger.LogInformation("请求写入文件: 相对于用户目录的路径 '{RelativeFilePath}', 用户目录: '{UserDirectory}'", relativeFilePath, userDirectory);

            try
            {
                // --- 安全性与路径处理 ---
                // 1. 检查 relativeFilePath 是否为空或仅包含空白字符
                if (string.IsNullOrWhiteSpace(relativeFilePath))
                {
                    _logger.LogWarning("提供的相对文件路径为空或无效。");
                    return "错误：文件路径不能为空。";
                }

                // 2. 规范化相对路径，移除开头可能存在的路径分隔符，防止 Path.Combine 行为异常
                relativeFilePath = relativeFilePath.TrimStart(Path.DirectorySeparatorChar, Path.AltDirectorySeparatorChar);
                if (string.IsNullOrWhiteSpace(relativeFilePath)) // 再次检查，如果路径只包含分隔符
                {
                    _logger.LogWarning("提供的相对文件路径在清理后为空或无效。");
                    return "错误：文件路径无效。";
                }


                // 3. 检查 relativeFilePath 是否包含 '..' 或尝试使用绝对路径
                if (relativeFilePath.Split(Path.DirectorySeparatorChar, Path.AltDirectorySeparatorChar).Contains(".."))
                {
                    _logger.LogWarning("相对路径 '{RelativeFilePath}' 包含 '..'，不允许。", relativeFilePath);
                    return "错误：相对路径中不允许使用 '..'。";
                }
                if (Path.IsPathRooted(relativeFilePath))
                {
                    _logger.LogWarning("相对路径 '{RelativeFilePath}' 似乎是一个绝对路径，不允许。", relativeFilePath);
                    return "错误：只允许提供相对于用户目录的路径。";
                }


                // 4. 组合路径并规范化
                var fullPath = Path.GetFullPath(Path.Combine(userDirectory, relativeFilePath));
                var fullUserDirectory = Path.GetFullPath(userDirectory);

                // 5. 验证最终的 fullPath 是否在用户目录下，并且不是用户目录本身
                if (fullPath == fullUserDirectory)
                {
                    _logger.LogWarning("拒绝写入：目标路径 '{FullPath}' 是用户目录本身。提供的相对路径: '{RelativeFilePath}'", fullPath, relativeFilePath);
                    return $"错误：不能直接写入用户目录。请在路径 '{relativeFilePath}' 中指定一个有效的文件名。";
                }
                // 确保 fullPath 是以 fullUserDirectory 开头的，并且后面紧跟路径分隔符
                if (!fullPath.StartsWith(fullUserDirectory + Path.DirectorySeparatorChar))
                {
                    _logger.LogWarning("拒绝访问：计算出的完整路径 '{FullPath}' (来自相对路径 '{RelativeFilePath}') 不在授权的用户目录 '{UserDirectory}' 内。", fullPath, relativeFilePath, fullUserDirectory);
                    return $"错误：无权写入指定路径 '{relativeFilePath}'。路径必须在用户目录内。";
                }

                // 6. 检查文件名是否有效 (GetFileName 会从 fullPath 中提取最后一部分)
                var fileName = Path.GetFileName(fullPath);
                if (string.IsNullOrWhiteSpace(fileName) || fileName.IndexOfAny(Path.GetInvalidFileNameChars()) >= 0)
                {
                    _logger.LogWarning("从路径 '{RelativeFilePath}' 解析出的文件名 '{FileName}' 无效。", relativeFilePath, fileName);
                    return $"错误：文件名 '{fileName}' (来自路径 '{relativeFilePath}') 无效。";
                }

                // 7. 检查内容大小 (以字节为单位)
                if (Encoding.UTF8.GetByteCount(content) > _maxWriteSizeBytes)
                {
                    _logger.LogWarning("写入内容过大: {FullPath} (请求大小: {ContentLength} bytes, 最大允许: {MaxBytes} bytes)",
                        fullPath, Encoding.UTF8.GetByteCount(content), _maxWriteSizeBytes);
                    return $"错误：要写入的内容过大 (超过 {_maxWriteSizeBytes / 1024 / 1024}MB)。";
                }

                // --- 目录与文件操作 ---
                var directoryName = Path.GetDirectoryName(fullPath);
                // Path.GetDirectoryName 对于像 "file.txt" 这样的路径会返回 string.Empty (如果当前目录是根) 或 null (如果路径无效)
                // 但由于我们已经组合了 userDirectory，directoryName 应该总是有效的，并且至少是 userDirectory
                if (string.IsNullOrEmpty(directoryName)) // 理论上不应发生，因为 fullPath 是规范化的且在 userDirectory 下
                {
                    _logger.LogError("无法从完整路径 '{FullPath}' 获取目录名。", fullPath);
                    return $"错误：无法确定文件的目标目录。";
                }

                // 再次确认目录名仍在用户目录下 (这一步是双重保险)
                if (!Path.GetFullPath(directoryName).StartsWith(fullUserDirectory))
                {
                    _logger.LogError("内部安全错误：计算出的目录 '{DirectoryName}' 不在用户目录 '{UserDirectory}' 内。操作中止。", directoryName, fullUserDirectory);
                    return "错误：目标目录解析错误，操作被中止。";
                }

                if (!Directory.Exists(directoryName))
                {
                    _logger.LogInformation("目标目录不存在，正在创建: {DirectoryName}", directoryName);
                    Directory.CreateDirectory(directoryName); // 如果失败或无权限会抛出异常
                }

                // 写入文件 (异步，覆盖现有文件，使用UTF-8编码)
                await File.WriteAllTextAsync(fullPath, content, Encoding.UTF8);
                _logger.LogInformation("成功写入文件: {FullPath}", fullPath);
                // 返回给用户的文件名应该是他们提供的相对路径，或者只是文件名部分
                return $"文件 '{Path.GetFileName(relativeFilePath)}' 已成功保存。";

            }
            catch (ArgumentException ex) // Path.Combine, GetFullPath, GetDirectoryName, GetFileName 等可能抛出
            {
                _logger.LogError(ex, "写入文件时路径参数无效: '{RelativeFilePath}'", relativeFilePath);
                return $"错误：提供的文件路径 '{relativeFilePath}' 无效或包含非法字符。 {ex.Message}";
            }
            catch (SecurityException ex)
            {
                _logger.LogError(ex, "写入文件时发生安全错误: '{RelativeFilePath}'", relativeFilePath);
                return $"错误：写入文件 '{Path.GetFileName(relativeFilePath)}' 时权限不足。";
            }
            catch (IOException ex) // 包括 DirectoryNotFoundException, PathTooLongException, DriveNotFoundException
            {
                _logger.LogError(ex, "写入文件或创建目录时发生 IO 错误: '{RelativeFilePath}'", relativeFilePath);
                return $"错误：写入文件 '{Path.GetFileName(relativeFilePath)}' 或创建其目录时发生 IO 错误: {ex.Message}";
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogError(ex, "写入文件或创建目录时发生未经授权的访问: '{RelativeFilePath}'", relativeFilePath);
                return $"错误：写入文件 '{Path.GetFileName(relativeFilePath)}' 或创建其目录时未经授权。请检查权限。";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "写入文件时发生意外错误: '{RelativeFilePath}'", relativeFilePath);
                return $"错误：写入文件 '{Path.GetFileName(relativeFilePath)}' 时发生未知错误。";
            }
        }
    }
}