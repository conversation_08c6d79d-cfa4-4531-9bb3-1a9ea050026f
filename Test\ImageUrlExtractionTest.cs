using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace Yidev.LocalAI.Tests
{
    public class ImageUrlExtractionTest
    {
        public static void TestImageUrlExtraction()
        {
            var testCases = new[]
            {
                // Markdown格式
                "![示例图片](https://picsum.photos/300/200?random=1)",
                "这是一张图片：![alt](https://example.com/image.jpg)",
                
                // HTML格式
                "<img src=\"https://picsum.photos/350/250?random=3\" alt=\"HTML图片\" />",
                "<img src='https://example.com/test.png' />",
                
                // 直接URL
                "https://picsum.photos/300/300?random=6",
                "请看这个链接：https://example.com/photo.jpeg",
                
                // 混合格式
                @"这里有多种格式的图片：

Markdown格式：
![图片1](https://picsum.photos/200/150?random=9)

HTML格式：
<img src=""https://picsum.photos/250/180?random=10"" alt=""图片2"" />

直接URL：
https://picsum.photos/300/200?random=11

文本和图片混合显示效果很好！"
            };

            foreach (var testCase in testCases)
            {
                Console.WriteLine($"测试内容: {testCase}");
                var urls = ExtractImageUrls(testCase);
                Console.WriteLine($"提取到的URL数量: {urls.Count}");
                foreach (var url in urls)
                {
                    Console.WriteLine($"  - {url}");
                }
                Console.WriteLine("---");
            }
        }

        private static List<string> ExtractImageUrls(string content)
        {
            var imageUrls = new List<string>();

            if (string.IsNullOrWhiteSpace(content))
                return imageUrls;

            // 正则表达式匹配各种图片格式
            var patterns = new[]
            {
                // Markdown图片语法: ![alt](url)
                @"!\[.*?\]\((https?://[^\s\)]+\.(?:jpg|jpeg|png|gif|bmp|webp|svg)(?:\?[^\s\)]*)?)\)",
                // HTML img标签: <img src="url" />
                @"<img[^>]+src=[""']?(https?://[^\s""'>]+\.(?:jpg|jpeg|png|gif|bmp|webp|svg)(?:\?[^\s""'>]*)?)[""']?[^>]*>",
                // 直接的图片URL (独立一行或被空格包围)
                @"(?:^|\s)(https?://[^\s]+\.(?:jpg|jpeg|png|gif|bmp|webp|svg)(?:\?[^\s]*)?)(?:\s|$)"
            };

            foreach (var pattern in patterns)
            {
                Console.WriteLine($"使用正则: {pattern}");
                var matches = Regex.Matches(content, pattern,
                    RegexOptions.IgnoreCase | RegexOptions.Multiline);

                Console.WriteLine($"匹配数量: {matches.Count}");
                foreach (Match match in matches)
                {
                    Console.WriteLine($"完整匹配: {match.Value}");
                    Console.WriteLine($"组数量: {match.Groups.Count}");
                    for (int i = 0; i < match.Groups.Count; i++)
                    {
                        Console.WriteLine($"  组{i}: {match.Groups[i].Value}");
                    }
                    
                    var url = match.Groups[1].Value.Trim();
                    if (!string.IsNullOrWhiteSpace(url) && !imageUrls.Contains(url))
                    {
                        imageUrls.Add(url);
                        Console.WriteLine($"添加URL: {url}");
                    }
                }
            }

            return imageUrls;
        }
    }
}
