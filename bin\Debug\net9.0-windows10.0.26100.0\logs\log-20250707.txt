2025-07-07 10:10:28.341 +08:00 [INF] Application Shutting Down
2025-07-07 10:10:28.355 +08:00 [DBG] Hosting stopping
2025-07-07 10:10:28.356 +08:00 [INF] Application is shutting down...
2025-07-07 10:10:28.358 +08:00 [DBG] Hosting stopped
2025-07-07 10:17:33.330 +08:00 [DBG] Hosting starting
2025-07-07 10:17:33.414 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 10:17:33.420 +08:00 [INF] Hosting environment: Production
2025-07-07 10:17:33.423 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-07 10:17:33.425 +08:00 [DBG] Hosting started
2025-07-07 10:17:33.426 +08:00 [INF] Application Starting Up
2025-07-07 10:17:36.674 +08:00 [DBG] warn: 2025/7/7 10:17:36.674 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-07 10:17:36.848 +08:00 [DBG] info: 2025/7/7 10:17:36.848 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-07 10:17:36.854 +08:00 [INF] TopicService initialized and database ensured.
2025-07-07 10:17:37.524 +08:00 [INF] Initializing SemanticKernelService...
2025-07-07 10:17:37.872 +08:00 [INF] SemanticKernelService initialized.
2025-07-07 10:17:41.626 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-07 10:17:41.632 +08:00 [INF] Getting topics for user: llk
2025-07-07 10:17:42.134 +08:00 [DBG] info: 2025/7/7 10:17:42.134 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-07 10:17:42.193 +08:00 [INF] Getting messages for topic ID: 9
2025-07-07 10:17:42.207 +08:00 [DBG] info: 2025/7/7 10:17:42.207 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='9'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 10:17:42.230 +08:00 [INF] Setting conversation history. Message count: 4
2025-07-07 10:17:48.151 +08:00 [INF] Creating topic '新话题 10:17:48' for user: llk
2025-07-07 10:17:48.256 +08:00 [DBG] info: 2025/7/7 10:17:48.256 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='2025-07-07T10:17:48.1538272+08:00' (DbType = DateTime), @p1='新话题 10:17:48' (Nullable = false) (Size = 12), @p2='llk' (Nullable = false) (Size = 3)], CommandType='Text', CommandTimeout='30']
      INSERT INTO "Topics" ("CreatedAt", "Name", "UserName")
      VALUES (@p0, @p1, @p2)
      RETURNING "Id";
2025-07-07 10:17:48.300 +08:00 [INF] Topic '新话题 10:17:48' created with ID: 10
2025-07-07 10:17:48.306 +08:00 [INF] Getting messages for topic ID: 10
2025-07-07 10:17:48.310 +08:00 [DBG] info: 2025/7/7 10:17:48.310 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='10'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 10:17:48.312 +08:00 [INF] Setting conversation history. Message count: 0
2025-07-07 10:17:53.159 +08:00 [INF] Adding message to topic ID: 10
2025-07-07 10:17:53.168 +08:00 [DBG] info: 2025/7/7 10:17:53.168 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='你好' (Nullable = false) (Size = 2), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-07T10:17:53.1587435+08:00' (DbType = DateTime), @p4='10'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-07 10:17:53.178 +08:00 [INF] Generating topic title for user message: 你好
2025-07-07 10:17:54.217 +08:00 [ERR] Error generating topic title
System.Text.Json.JsonReaderException: '0x1F' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
   at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.JsonDocument.Parse(ReadOnlySpan`1 utf8JsonSpan, JsonReaderOptions readerOptions, MetadataDb& database, StackRowStack& stack)
   at System.Text.Json.JsonDocument.Parse(ReadOnlyMemory`1 utf8Json, JsonReaderOptions readerOptions, Byte[] extraRentedArrayPoolBytes, PooledByteBufferWriter extraPooledByteBufferWriter)
   at System.Text.Json.JsonDocument.Parse(ReadOnlyMemory`1 utf8Json, JsonDocumentOptions options)
   at OpenAI.Chat.ChatCompletion.op_Explicit(ClientResult result)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(IEnumerable`1 messages, ChatCompletionOptions options, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.GetChatMessageContentsAsync(String targetModel, ChatHistory chatHistory, PromptExecutionSettings executionSettings, Kernel kernel, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.ChatCompletion.ChatCompletionServiceExtensions.GetChatMessageContentAsync(IChatCompletionService chatCompletionService, ChatHistory chatHistory, PromptExecutionSettings executionSettings, Kernel kernel, CancellationToken cancellationToken)
   at Yidev.LocalAI.Services.SemanticKernelService.GenerateTopicTitleAsync(String userMessage) in D:\yidev\Source\yidev.localai\Services\SemanticKernelService.cs:line 146
2025-07-07 10:17:54.258 +08:00 [INF] Updating topic ID: 10
2025-07-07 10:17:54.264 +08:00 [DBG] info: 2025/7/7 10:17:54.263 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p3='10', @p0='2025-07-07T10:17:48.1538272+08:00' (DbType = DateTime), @p1='你好' (Nullable = false) (Size = 2), @p2='llk' (Nullable = false) (Size = 3)], CommandType='Text', CommandTimeout='30']
      UPDATE "Topics" SET "CreatedAt" = @p0, "Name" = @p1, "UserName" = @p2
      WHERE "Id" = @p3
      RETURNING 1;
2025-07-07 10:17:54.273 +08:00 [INF] Getting chat response for user message: 你好
2025-07-07 10:19:26.052 +08:00 [DBG] Hosting starting
2025-07-07 10:19:26.113 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 10:19:26.120 +08:00 [INF] Hosting environment: Production
2025-07-07 10:19:26.122 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-07 10:19:26.124 +08:00 [DBG] Hosting started
2025-07-07 10:19:26.126 +08:00 [INF] Application Starting Up
2025-07-07 10:19:27.083 +08:00 [DBG] warn: 2025/7/7 10:19:27.082 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-07 10:19:27.261 +08:00 [DBG] info: 2025/7/7 10:19:27.261 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (26ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-07 10:19:27.266 +08:00 [INF] TopicService initialized and database ensured.
2025-07-07 10:19:27.433 +08:00 [INF] Initializing SemanticKernelService...
2025-07-07 10:19:27.453 +08:00 [INF] SemanticKernelService initialized.
2025-07-07 10:19:31.131 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-07 10:19:31.136 +08:00 [INF] Getting topics for user: llk
2025-07-07 10:19:31.634 +08:00 [DBG] info: 2025/7/7 10:19:31.634 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-07 10:19:31.694 +08:00 [INF] Getting messages for topic ID: 10
2025-07-07 10:19:31.709 +08:00 [DBG] info: 2025/7/7 10:19:31.709 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='10'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 10:19:31.732 +08:00 [INF] Setting conversation history. Message count: 1
2025-07-07 10:19:38.006 +08:00 [INF] Adding message to topic ID: 10
2025-07-07 10:19:38.116 +08:00 [DBG] info: 2025/7/7 10:19:38.115 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='你好' (Nullable = false) (Size = 2), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-07T10:19:38.0056740+08:00' (DbType = DateTime), @p4='10'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-07 10:19:38.160 +08:00 [INF] Getting chat response for user message: 你好
2025-07-07 10:20:06.233 +08:00 [DBG] Hosting starting
2025-07-07 10:20:06.293 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 10:20:06.299 +08:00 [INF] Hosting environment: Production
2025-07-07 10:20:06.302 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-07 10:20:06.304 +08:00 [DBG] Hosting started
2025-07-07 10:20:06.305 +08:00 [INF] Application Starting Up
2025-07-07 10:20:07.255 +08:00 [DBG] warn: 2025/7/7 10:20:07.255 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-07 10:20:07.436 +08:00 [DBG] info: 2025/7/7 10:20:07.436 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (22ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-07 10:20:07.442 +08:00 [INF] TopicService initialized and database ensured.
2025-07-07 10:20:07.616 +08:00 [INF] Initializing SemanticKernelService...
2025-07-07 10:20:07.635 +08:00 [INF] SemanticKernelService initialized.
2025-07-07 10:20:10.494 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-07 10:20:10.499 +08:00 [INF] Getting topics for user: llk
2025-07-07 10:20:11.004 +08:00 [DBG] info: 2025/7/7 10:20:11.004 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-07 10:20:11.065 +08:00 [INF] Getting messages for topic ID: 10
2025-07-07 10:20:11.080 +08:00 [DBG] info: 2025/7/7 10:20:11.080 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='10'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 10:20:11.102 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-07 10:20:16.265 +08:00 [INF] Adding message to topic ID: 10
2025-07-07 10:20:16.373 +08:00 [DBG] info: 2025/7/7 10:20:16.373 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='你好' (Nullable = false) (Size = 2), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-07T10:20:16.2647830+08:00' (DbType = DateTime), @p4='10'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-07 10:20:16.400 +08:00 [INF] Getting chat response for user message: 你好
2025-07-07 10:44:13.804 +08:00 [DBG] Hosting starting
2025-07-07 10:44:13.891 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 10:44:13.897 +08:00 [INF] Hosting environment: Production
2025-07-07 10:44:13.900 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-07 10:44:13.901 +08:00 [DBG] Hosting started
2025-07-07 10:44:13.903 +08:00 [INF] Application Starting Up
2025-07-07 10:44:17.166 +08:00 [DBG] warn: 2025/7/7 10:44:17.166 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-07 10:44:17.352 +08:00 [DBG] info: 2025/7/7 10:44:17.352 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (30ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-07 10:44:17.358 +08:00 [INF] TopicService initialized and database ensured.
2025-07-07 10:44:18.005 +08:00 [INF] Initializing SemanticKernelService...
2025-07-07 10:44:18.348 +08:00 [INF] SemanticKernelService initialized.
2025-07-07 10:44:21.837 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-07 10:44:21.843 +08:00 [INF] Getting topics for user: llk
2025-07-07 10:44:22.344 +08:00 [DBG] info: 2025/7/7 10:44:22.344 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-07 10:44:22.404 +08:00 [INF] Getting messages for topic ID: 10
2025-07-07 10:44:22.420 +08:00 [DBG] info: 2025/7/7 10:44:22.420 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='10'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 10:44:22.442 +08:00 [INF] Setting conversation history. Message count: 3
2025-07-07 10:44:29.344 +08:00 [INF] Adding message to topic ID: 10
2025-07-07 10:44:29.457 +08:00 [DBG] info: 2025/7/7 10:44:29.457 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='你好' (Nullable = false) (Size = 2), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-07T10:44:29.3441439+08:00' (DbType = DateTime), @p4='10'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-07 10:44:29.484 +08:00 [INF] Getting chat response for user message: 你好
2025-07-07 10:44:47.212 +08:00 [DBG] Hosting starting
2025-07-07 10:44:47.275 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 10:44:47.281 +08:00 [INF] Hosting environment: Production
2025-07-07 10:44:47.283 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-07 10:44:47.285 +08:00 [DBG] Hosting started
2025-07-07 10:44:47.287 +08:00 [INF] Application Starting Up
2025-07-07 10:44:48.268 +08:00 [DBG] warn: 2025/7/7 10:44:48.267 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-07 10:44:48.445 +08:00 [DBG] info: 2025/7/7 10:44:48.445 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (25ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-07 10:44:48.451 +08:00 [INF] TopicService initialized and database ensured.
2025-07-07 10:44:48.617 +08:00 [INF] Initializing SemanticKernelService...
2025-07-07 10:44:48.635 +08:00 [INF] SemanticKernelService initialized.
2025-07-07 10:44:51.363 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-07 10:44:51.368 +08:00 [INF] Getting topics for user: llk
2025-07-07 10:44:51.870 +08:00 [DBG] info: 2025/7/7 10:44:51.870 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-07 10:44:51.929 +08:00 [INF] Getting messages for topic ID: 10
2025-07-07 10:44:51.945 +08:00 [DBG] info: 2025/7/7 10:44:51.945 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='10'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 10:44:51.968 +08:00 [INF] Setting conversation history. Message count: 4
2025-07-07 10:44:56.460 +08:00 [INF] Adding message to topic ID: 10
2025-07-07 10:44:56.578 +08:00 [DBG] info: 2025/7/7 10:44:56.578 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='你好' (Nullable = false) (Size = 2), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-07T10:44:56.4592618+08:00' (DbType = DateTime), @p4='10'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-07 10:44:56.724 +08:00 [INF] Getting chat response for user message: 你好
2025-07-07 10:50:19.578 +08:00 [DBG] Hosting starting
2025-07-07 10:50:19.637 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 10:50:19.644 +08:00 [INF] Hosting environment: Production
2025-07-07 10:50:19.646 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-07 10:50:19.647 +08:00 [DBG] Hosting started
2025-07-07 10:50:19.648 +08:00 [INF] Application Starting Up
2025-07-07 10:50:20.596 +08:00 [DBG] warn: 2025/7/7 10:50:20.595 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-07 10:50:20.769 +08:00 [DBG] info: 2025/7/7 10:50:20.769 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (22ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-07 10:50:20.775 +08:00 [INF] TopicService initialized and database ensured.
2025-07-07 10:50:20.944 +08:00 [INF] Initializing SemanticKernelService...
2025-07-07 10:50:20.963 +08:00 [INF] SemanticKernelService initialized.
2025-07-07 10:50:24.010 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-07 10:50:24.016 +08:00 [INF] Getting topics for user: llk
2025-07-07 10:50:24.522 +08:00 [DBG] info: 2025/7/7 10:50:24.522 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-07 10:50:24.581 +08:00 [INF] Getting messages for topic ID: 10
2025-07-07 10:50:24.596 +08:00 [DBG] info: 2025/7/7 10:50:24.596 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='10'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 10:50:24.619 +08:00 [INF] Setting conversation history. Message count: 5
2025-07-07 10:50:26.881 +08:00 [INF] Deleting topic ID: 10
2025-07-07 10:50:26.891 +08:00 [DBG] info: 2025/7/7 10:50:26.891 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topic_Id_0='10'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topic_Id_0
2025-07-07 10:50:26.997 +08:00 [DBG] info: 2025/7/7 10:50:26.997 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='24'], CommandType='Text', CommandTimeout='30']
      DELETE FROM "ChatMessages"
      WHERE "Id" = @p0
      RETURNING 1;
2025-07-07 10:50:27.003 +08:00 [DBG] info: 2025/7/7 10:50:27.003 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='25'], CommandType='Text', CommandTimeout='30']
      DELETE FROM "ChatMessages"
      WHERE "Id" = @p0
      RETURNING 1;
2025-07-07 10:50:27.005 +08:00 [DBG] info: 2025/7/7 10:50:27.005 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='26'], CommandType='Text', CommandTimeout='30']
      DELETE FROM "ChatMessages"
      WHERE "Id" = @p0
      RETURNING 1;
2025-07-07 10:50:27.009 +08:00 [DBG] info: 2025/7/7 10:50:27.009 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='27'], CommandType='Text', CommandTimeout='30']
      DELETE FROM "ChatMessages"
      WHERE "Id" = @p0
      RETURNING 1;
2025-07-07 10:50:27.011 +08:00 [DBG] info: 2025/7/7 10:50:27.011 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='28'], CommandType='Text', CommandTimeout='30']
      DELETE FROM "ChatMessages"
      WHERE "Id" = @p0
      RETURNING 1;
2025-07-07 10:50:27.014 +08:00 [DBG] info: 2025/7/7 10:50:27.014 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='10'], CommandType='Text', CommandTimeout='30']
      DELETE FROM "Topics"
      WHERE "Id" = @p0
      RETURNING 1;
2025-07-07 10:50:27.048 +08:00 [INF] Topic ID: 10 deleted.
2025-07-07 10:50:29.353 +08:00 [INF] Creating topic '新话题 10:50:29' for user: llk
2025-07-07 10:50:29.379 +08:00 [DBG] info: 2025/7/7 10:50:29.379 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='2025-07-07T10:50:29.3552224+08:00' (DbType = DateTime), @p1='新话题 10:50:29' (Nullable = false) (Size = 12), @p2='llk' (Nullable = false) (Size = 3)], CommandType='Text', CommandTimeout='30']
      INSERT INTO "Topics" ("CreatedAt", "Name", "UserName")
      VALUES (@p0, @p1, @p2)
      RETURNING "Id";
2025-07-07 10:50:29.395 +08:00 [INF] Topic '新话题 10:50:29' created with ID: 11
2025-07-07 10:50:29.397 +08:00 [INF] Getting messages for topic ID: 11
2025-07-07 10:50:29.402 +08:00 [DBG] info: 2025/7/7 10:50:29.402 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='11'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 10:50:29.404 +08:00 [INF] Setting conversation history. Message count: 0
2025-07-07 10:50:34.604 +08:00 [INF] Adding message to topic ID: 11
2025-07-07 10:50:34.612 +08:00 [DBG] info: 2025/7/7 10:50:34.612 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='你好' (Nullable = false) (Size = 2), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-07T10:50:34.6039057+08:00' (DbType = DateTime), @p4='11'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-07 10:50:34.622 +08:00 [INF] Generating topic title for user message: 你好
2025-07-07 10:50:35.653 +08:00 [ERR] Error generating topic title
System.Text.Json.JsonReaderException: '0x1F' is an invalid start of a value. LineNumber: 0 | BytePositionInLine: 0.
   at System.Text.Json.ThrowHelper.ThrowJsonReaderException(Utf8JsonReader& json, ExceptionResource resource, Byte nextByte, ReadOnlySpan`1 bytes)
   at System.Text.Json.Utf8JsonReader.ConsumeValue(Byte marker)
   at System.Text.Json.Utf8JsonReader.ReadFirstToken(Byte first)
   at System.Text.Json.Utf8JsonReader.ReadSingleSegment()
   at System.Text.Json.Utf8JsonReader.Read()
   at System.Text.Json.JsonDocument.Parse(ReadOnlySpan`1 utf8JsonSpan, JsonReaderOptions readerOptions, MetadataDb& database, StackRowStack& stack)
   at System.Text.Json.JsonDocument.Parse(ReadOnlyMemory`1 utf8Json, JsonReaderOptions readerOptions, Byte[] extraRentedArrayPoolBytes, PooledByteBufferWriter extraPooledByteBufferWriter)
   at System.Text.Json.JsonDocument.Parse(ReadOnlyMemory`1 utf8Json, JsonDocumentOptions options)
   at OpenAI.Chat.ChatCompletion.op_Explicit(ClientResult result)
   at OpenAI.Chat.ChatClient.CompleteChatAsync(IEnumerable`1 messages, ChatCompletionOptions options, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.RunRequestAsync[T](Func`1 request)
   at Microsoft.SemanticKernel.Connectors.OpenAI.ClientCore.GetChatMessageContentsAsync(String targetModel, ChatHistory chatHistory, PromptExecutionSettings executionSettings, Kernel kernel, CancellationToken cancellationToken)
   at Microsoft.SemanticKernel.ChatCompletion.ChatCompletionServiceExtensions.GetChatMessageContentAsync(IChatCompletionService chatCompletionService, ChatHistory chatHistory, PromptExecutionSettings executionSettings, Kernel kernel, CancellationToken cancellationToken)
   at Yidev.LocalAI.Services.SemanticKernelService.GenerateTopicTitleAsync(String userMessage) in D:\yidev\Source\yidev.localai\Services\SemanticKernelService.cs:line 146
2025-07-07 10:50:35.693 +08:00 [INF] Updating topic ID: 11
2025-07-07 10:50:35.697 +08:00 [DBG] info: 2025/7/7 10:50:35.697 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p3='11', @p0='2025-07-07T10:50:29.3552224+08:00' (DbType = DateTime), @p1='你好' (Nullable = false) (Size = 2), @p2='llk' (Nullable = false) (Size = 3)], CommandType='Text', CommandTimeout='30']
      UPDATE "Topics" SET "CreatedAt" = @p0, "Name" = @p1, "UserName" = @p2
      WHERE "Id" = @p3
      RETURNING 1;
2025-07-07 10:50:35.701 +08:00 [INF] Getting chat response for user message: 你好
2025-07-07 10:51:21.661 +08:00 [DBG] Hosting starting
2025-07-07 10:51:21.726 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 10:51:21.733 +08:00 [INF] Hosting environment: Production
2025-07-07 10:51:21.735 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-07 10:51:21.737 +08:00 [DBG] Hosting started
2025-07-07 10:51:21.739 +08:00 [INF] Application Starting Up
2025-07-07 10:51:22.685 +08:00 [DBG] warn: 2025/7/7 10:51:22.685 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-07 10:51:22.860 +08:00 [DBG] info: 2025/7/7 10:51:22.860 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (24ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-07 10:51:22.865 +08:00 [INF] TopicService initialized and database ensured.
2025-07-07 10:51:23.029 +08:00 [INF] Initializing SemanticKernelService...
2025-07-07 10:51:23.048 +08:00 [INF] SemanticKernelService initialized.
2025-07-07 10:51:27.401 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-07 10:51:27.406 +08:00 [INF] Getting topics for user: llk
2025-07-07 10:51:27.926 +08:00 [DBG] info: 2025/7/7 10:51:27.926 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-07 10:51:27.986 +08:00 [INF] Getting messages for topic ID: 11
2025-07-07 10:51:28.001 +08:00 [DBG] info: 2025/7/7 10:51:28.001 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='11'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 10:51:28.025 +08:00 [INF] Setting conversation history. Message count: 1
2025-07-07 10:55:51.193 +08:00 [INF] Application Shutting Down
2025-07-07 10:55:51.197 +08:00 [DBG] Hosting stopping
2025-07-07 10:55:51.200 +08:00 [INF] Application is shutting down...
2025-07-07 10:55:51.206 +08:00 [DBG] Hosting stopped
2025-07-07 11:11:59.126 +08:00 [DBG] Hosting starting
2025-07-07 11:11:59.186 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 11:11:59.193 +08:00 [INF] Hosting environment: Production
2025-07-07 11:11:59.197 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-07 11:11:59.199 +08:00 [DBG] Hosting started
2025-07-07 11:11:59.200 +08:00 [INF] Application Starting Up
2025-07-07 11:12:00.163 +08:00 [DBG] warn: 2025/7/7 11:12:00.163 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-07 11:12:00.327 +08:00 [DBG] info: 2025/7/7 11:12:00.327 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-07 11:12:00.333 +08:00 [INF] TopicService initialized and database ensured.
2025-07-07 11:12:00.501 +08:00 [INF] Initializing SemanticKernelService...
2025-07-07 11:12:00.521 +08:00 [INF] SemanticKernelService initialized.
2025-07-07 11:12:03.356 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-07 11:12:03.362 +08:00 [INF] Getting topics for user: llk
2025-07-07 11:12:03.862 +08:00 [DBG] info: 2025/7/7 11:12:03.862 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-07 11:12:03.923 +08:00 [INF] Getting messages for topic ID: 11
2025-07-07 11:12:03.938 +08:00 [DBG] info: 2025/7/7 11:12:03.938 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='11'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 11:12:03.961 +08:00 [INF] Setting conversation history. Message count: 1
2025-07-07 11:12:09.103 +08:00 [INF] Adding message to topic ID: 11
2025-07-07 11:12:09.214 +08:00 [DBG] info: 2025/7/7 11:12:09.214 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='你好' (Nullable = false) (Size = 2), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-07T11:12:09.1024630+08:00' (DbType = DateTime), @p4='11'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-07 11:12:09.242 +08:00 [INF] Getting chat response for user message: 你好
2025-07-07 11:12:18.310 +08:00 [INF] Received chat response: 好的，我明白了。从现在开始，在回答您的问题后，我将尝试提出一个相关问题。

那么，我们现在可以开始做什么呢？比如，您可以让我帮您查找文件、读取代码，或者整理项目目录。
2025-07-07 11:12:18.313 +08:00 [INF] Adding message to topic ID: 11
2025-07-07 11:12:18.316 +08:00 [DBG] info: 2025/7/7 11:12:18.316 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='好的，我明白了。从现在开始，在回答您的问题后，我将尝试提出一个相关问题。

那么，我们现在可以开始做什么呢？比如，您可以让我帮您查找文件、读取代码，或者整理项目目录。' (Nullable = false) (Size = 83), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-07T11:12:18.3132240+08:00' (DbType = DateTime), @p4='11'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-07 11:13:17.404 +08:00 [INF] Adding message to topic ID: 11
2025-07-07 11:13:17.406 +08:00 [DBG] info: 2025/7/7 11:13:17.406 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='这是什么大模型？' (Nullable = false) (Size = 8), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-07T11:13:17.4040639+08:00' (DbType = DateTime), @p4='11'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-07 11:13:17.532 +08:00 [INF] Getting chat response for user message: 这是什么大模型？
2025-07-07 11:13:35.273 +08:00 [INF] Received chat response: 我是 Gemini，一个由 Google 训练的大型语言模型。我被集成到这个 C# WPF 应用中，作为您的 AI 助手。

感谢您的说明，我会记住在每次回答后向您提问，以更好地帮助您。

那么，我们现在可以开始做什么呢？比如，您可以让我帮您查找文件、读取代码，或者整理项目目录。
2025-07-07 11:13:35.275 +08:00 [INF] Adding message to topic ID: 11
2025-07-07 11:13:35.278 +08:00 [DBG] info: 2025/7/7 11:13:35.278 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='我是 Gemini，一个由 Google 训练的大型语言模型。我被集成到这个 C# WPF 应用中，作为您的 AI 助手。

感谢您的说明，我会记住在每次回答后向您提问，以更好地帮助您。

那么，我们现在可以开始做什么呢？比如，您可以让我帮您查找文件、读取代码，或者整理项目目录。' (Nullable = false) (Size = 140), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-07T11:13:35.2754719+08:00' (DbType = DateTime), @p4='11'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-07 11:18:06.997 +08:00 [INF] Application Shutting Down
2025-07-07 11:18:07.001 +08:00 [DBG] Hosting stopping
2025-07-07 11:18:07.004 +08:00 [INF] Application is shutting down...
2025-07-07 11:18:07.008 +08:00 [DBG] Hosting stopped
2025-07-07 11:18:17.193 +08:00 [DBG] Hosting starting
2025-07-07 11:18:17.256 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 11:18:17.262 +08:00 [INF] Hosting environment: Production
2025-07-07 11:18:17.265 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-07 11:18:17.266 +08:00 [DBG] Hosting started
2025-07-07 11:18:17.268 +08:00 [INF] Application Starting Up
2025-07-07 11:18:18.215 +08:00 [DBG] warn: 2025/7/7 11:18:18.215 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-07 11:18:18.388 +08:00 [DBG] info: 2025/7/7 11:18:18.388 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-07 11:18:18.393 +08:00 [INF] TopicService initialized and database ensured.
2025-07-07 11:18:18.564 +08:00 [INF] Initializing SemanticKernelService...
2025-07-07 11:18:18.583 +08:00 [INF] SemanticKernelService initialized.
2025-07-07 11:18:21.357 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-07 11:18:21.363 +08:00 [INF] Getting topics for user: llk
2025-07-07 11:18:21.865 +08:00 [DBG] info: 2025/7/7 11:18:21.865 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-07 11:18:21.925 +08:00 [INF] Getting messages for topic ID: 11
2025-07-07 11:18:21.941 +08:00 [DBG] info: 2025/7/7 11:18:21.941 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='11'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 11:18:21.964 +08:00 [INF] Setting conversation history. Message count: 5
2025-07-07 14:38:20.582 +08:00 [INF] Application Shutting Down
2025-07-07 14:38:20.587 +08:00 [DBG] Hosting stopping
2025-07-07 14:38:20.589 +08:00 [INF] Application is shutting down...
2025-07-07 14:38:20.594 +08:00 [DBG] Hosting stopped
2025-07-07 14:38:51.813 +08:00 [DBG] Hosting starting
2025-07-07 14:38:51.875 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 14:38:51.882 +08:00 [INF] Hosting environment: Production
2025-07-07 14:38:51.884 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-07 14:38:51.886 +08:00 [DBG] Hosting started
2025-07-07 14:38:51.888 +08:00 [INF] Application Starting Up
2025-07-07 14:38:52.852 +08:00 [DBG] warn: 2025/7/7 14:38:52.852 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-07 14:38:53.019 +08:00 [DBG] info: 2025/7/7 14:38:53.019 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-07 14:38:53.024 +08:00 [INF] TopicService initialized and database ensured.
2025-07-07 14:38:53.191 +08:00 [INF] Initializing SemanticKernelService...
2025-07-07 14:38:53.209 +08:00 [INF] SemanticKernelService initialized.
2025-07-07 14:38:58.177 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-07 14:38:58.182 +08:00 [INF] Getting topics for user: llk
2025-07-07 14:38:58.682 +08:00 [DBG] info: 2025/7/7 14:38:58.682 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-07 14:38:58.750 +08:00 [INF] Getting messages for topic ID: 11
2025-07-07 14:38:58.766 +08:00 [DBG] info: 2025/7/7 14:38:58.766 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='11'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 14:38:58.790 +08:00 [INF] Setting conversation history. Message count: 5
2025-07-07 14:39:01.820 +08:00 [INF] Creating topic '新话题 14:39:01' for user: llk
2025-07-07 14:39:01.930 +08:00 [DBG] info: 2025/7/7 14:39:01.930 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='2025-07-07T14:39:01.8227698+08:00' (DbType = DateTime), @p1='新话题 14:39:01' (Nullable = false) (Size = 12), @p2='llk' (Nullable = false) (Size = 3)], CommandType='Text', CommandTimeout='30']
      INSERT INTO "Topics" ("CreatedAt", "Name", "UserName")
      VALUES (@p0, @p1, @p2)
      RETURNING "Id";
2025-07-07 14:39:01.954 +08:00 [INF] Topic '新话题 14:39:01' created with ID: 12
2025-07-07 14:39:01.961 +08:00 [INF] Getting messages for topic ID: 12
2025-07-07 14:39:01.964 +08:00 [DBG] info: 2025/7/7 14:39:01.964 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='12'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 14:39:01.967 +08:00 [INF] Setting conversation history. Message count: 0
2025-07-07 14:39:06.641 +08:00 [INF] Adding message to topic ID: 12
2025-07-07 14:39:06.651 +08:00 [DBG] info: 2025/7/7 14:39:06.651 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='你好' (Nullable = false) (Size = 2), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-07T14:39:06.6412703+08:00' (DbType = DateTime), @p4='12'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-07 14:39:06.669 +08:00 [INF] Generating topic title for user message: 你好
2025-07-07 14:39:19.637 +08:00 [INF] Generated topic title: 回答后进行反问
2025-07-07 14:39:19.640 +08:00 [INF] Updating topic ID: 12
2025-07-07 14:39:19.645 +08:00 [DBG] info: 2025/7/7 14:39:19.645 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p3='12', @p0='2025-07-07T14:39:01.8227698+08:00' (DbType = DateTime), @p1='回答后进行反问' (Nullable = false) (Size = 7), @p2='llk' (Nullable = false) (Size = 3)], CommandType='Text', CommandTimeout='30']
      UPDATE "Topics" SET "CreatedAt" = @p0, "Name" = @p1, "UserName" = @p2
      WHERE "Id" = @p3
      RETURNING 1;
2025-07-07 14:39:19.656 +08:00 [INF] Getting chat response for user message: 你好
2025-07-07 14:39:29.639 +08:00 [INF] Received chat response: 好的，我明白了。在回答您的问题后，我会尽量提出一个相关的问题，以便更好地协助您。

请问现在有什么可以帮您的吗？
2025-07-07 14:39:29.641 +08:00 [INF] Adding message to topic ID: 12
2025-07-07 14:39:29.644 +08:00 [DBG] info: 2025/7/7 14:39:29.644 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='好的，我明白了。在回答您的问题后，我会尽量提出一个相关的问题，以便更好地协助您。

请问现在有什么可以帮您的吗？' (Nullable = false) (Size = 56), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-07T14:39:29.6416803+08:00' (DbType = DateTime), @p4='12'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-07 16:09:13.554 +08:00 [INF] Application Shutting Down
2025-07-07 16:09:13.559 +08:00 [DBG] Hosting stopping
2025-07-07 16:09:13.562 +08:00 [INF] Application is shutting down...
2025-07-07 16:09:13.567 +08:00 [DBG] Hosting stopped
2025-07-07 16:09:54.922 +08:00 [DBG] Hosting starting
2025-07-07 16:09:54.985 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 16:09:54.992 +08:00 [INF] Hosting environment: Production
2025-07-07 16:09:54.995 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-07 16:09:54.996 +08:00 [DBG] Hosting started
2025-07-07 16:09:54.998 +08:00 [INF] Application Starting Up
2025-07-07 16:09:55.965 +08:00 [DBG] warn: 2025/7/7 16:09:55.964 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-07 16:09:56.130 +08:00 [DBG] info: 2025/7/7 16:09:56.130 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-07 16:09:56.135 +08:00 [INF] TopicService initialized and database ensured.
2025-07-07 16:09:56.306 +08:00 [INF] Initializing SemanticKernelService...
2025-07-07 16:09:56.325 +08:00 [INF] SemanticKernelService initialized.
2025-07-07 16:09:59.502 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-07 16:09:59.508 +08:00 [INF] Getting topics for user: llk
2025-07-07 16:10:00.014 +08:00 [DBG] info: 2025/7/7 16:10:00.014 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (6ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-07 16:10:00.075 +08:00 [INF] Getting messages for topic ID: 12
2025-07-07 16:10:00.091 +08:00 [DBG] info: 2025/7/7 16:10:00.091 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='12'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 16:10:00.114 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-07 16:10:03.849 +08:00 [INF] Creating topic '新话题 16:10:03' for user: llk
2025-07-07 16:10:03.959 +08:00 [DBG] info: 2025/7/7 16:10:03.959 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='2025-07-07T16:10:03.8519263+08:00' (DbType = DateTime), @p1='新话题 16:10:03' (Nullable = false) (Size = 12), @p2='llk' (Nullable = false) (Size = 3)], CommandType='Text', CommandTimeout='30']
      INSERT INTO "Topics" ("CreatedAt", "Name", "UserName")
      VALUES (@p0, @p1, @p2)
      RETURNING "Id";
2025-07-07 16:10:04.044 +08:00 [INF] Topic '新话题 16:10:03' created with ID: 13
2025-07-07 16:10:04.049 +08:00 [INF] Getting messages for topic ID: 13
2025-07-07 16:10:04.053 +08:00 [DBG] info: 2025/7/7 16:10:04.053 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='13'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 16:10:04.057 +08:00 [INF] Setting conversation history. Message count: 0
2025-07-07 16:10:08.503 +08:00 [INF] Adding message to topic ID: 13
2025-07-07 16:10:08.513 +08:00 [DBG] info: 2025/7/7 16:10:08.513 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='你好' (Nullable = false) (Size = 2), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-07T16:10:08.5028464+08:00' (DbType = DateTime), @p4='13'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-07 16:10:08.522 +08:00 [INF] Generating topic title for user message: 你好
2025-07-07 16:10:17.405 +08:00 [INF] Generated topic title: 你好
2025-07-07 16:10:17.407 +08:00 [INF] Updating topic ID: 13
2025-07-07 16:10:17.412 +08:00 [DBG] info: 2025/7/7 16:10:17.412 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p3='13', @p0='2025-07-07T16:10:03.8519263+08:00' (DbType = DateTime), @p1='你好' (Nullable = false) (Size = 2), @p2='llk' (Nullable = false) (Size = 3)], CommandType='Text', CommandTimeout='30']
      UPDATE "Topics" SET "CreatedAt" = @p0, "Name" = @p1, "UserName" = @p2
      WHERE "Id" = @p3
      RETURNING 1;
2025-07-07 16:10:17.419 +08:00 [INF] Getting chat response for user message: 你好
2025-07-07 16:10:19.630 +08:00 [INF] Received chat response: 你好！我是一个集成在C# WPF应用中的AI助手。有什么可以帮助你的吗？
2025-07-07 16:10:19.632 +08:00 [INF] Adding message to topic ID: 13
2025-07-07 16:10:19.635 +08:00 [DBG] info: 2025/7/7 16:10:19.635 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='你好！我是一个集成在C# WPF应用中的AI助手。有什么可以帮助你的吗？' (Nullable = false) (Size = 36), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-07T16:10:19.6320677+08:00' (DbType = DateTime), @p4='13'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-07 16:15:41.728 +08:00 [INF] Application Shutting Down
2025-07-07 16:15:41.733 +08:00 [DBG] Hosting stopping
2025-07-07 16:15:41.737 +08:00 [INF] Application is shutting down...
2025-07-07 16:15:41.742 +08:00 [DBG] Hosting stopped
2025-07-07 16:18:09.423 +08:00 [DBG] Hosting starting
2025-07-07 16:18:09.486 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 16:18:09.492 +08:00 [INF] Hosting environment: Production
2025-07-07 16:18:09.495 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-07 16:18:09.497 +08:00 [DBG] Hosting started
2025-07-07 16:18:09.498 +08:00 [INF] Application Starting Up
2025-07-07 16:18:10.484 +08:00 [DBG] warn: 2025/7/7 16:18:10.483 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-07 16:18:10.652 +08:00 [DBG] info: 2025/7/7 16:18:10.652 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-07 16:18:10.658 +08:00 [INF] TopicService initialized and database ensured.
2025-07-07 16:18:10.833 +08:00 [INF] Initializing SemanticKernelService...
2025-07-07 16:18:10.852 +08:00 [INF] SemanticKernelService initialized.
2025-07-07 16:18:13.934 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-07 16:18:13.940 +08:00 [INF] Getting topics for user: llk
2025-07-07 16:18:14.464 +08:00 [DBG] info: 2025/7/7 16:18:14.464 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-07 16:18:14.525 +08:00 [INF] Getting messages for topic ID: 13
2025-07-07 16:18:14.540 +08:00 [DBG] info: 2025/7/7 16:18:14.540 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='13'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 16:18:14.562 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-07 16:18:37.197 +08:00 [INF] Updating topic ID: 13
2025-07-07 16:18:37.283 +08:00 [DBG] info: 2025/7/7 16:18:37.283 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p3='13', @p0='2025-07-07T16:10:03.8519263' (DbType = DateTime), @p1='你好啊' (Nullable = false) (Size = 3), @p2='llk' (Nullable = false) (Size = 3)], CommandType='Text', CommandTimeout='30']
      UPDATE "Topics" SET "CreatedAt" = @p0, "Name" = @p1, "UserName" = @p2
      WHERE "Id" = @p3
      RETURNING 1;
2025-07-07 16:19:00.687 +08:00 [INF] Application Shutting Down
2025-07-07 16:19:00.691 +08:00 [DBG] Hosting stopping
2025-07-07 16:19:00.692 +08:00 [INF] Application is shutting down...
2025-07-07 16:19:00.694 +08:00 [DBG] Hosting stopped
2025-07-07 16:19:42.306 +08:00 [DBG] Hosting starting
2025-07-07 16:19:42.370 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 16:19:42.377 +08:00 [INF] Hosting environment: Production
2025-07-07 16:19:42.380 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-07 16:19:42.381 +08:00 [DBG] Hosting started
2025-07-07 16:19:42.383 +08:00 [INF] Application Starting Up
2025-07-07 16:19:43.377 +08:00 [DBG] warn: 2025/7/7 16:19:43.376 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-07 16:19:43.544 +08:00 [DBG] info: 2025/7/7 16:19:43.544 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-07 16:19:43.549 +08:00 [INF] TopicService initialized and database ensured.
2025-07-07 16:19:43.717 +08:00 [INF] Initializing SemanticKernelService...
2025-07-07 16:19:43.737 +08:00 [INF] SemanticKernelService initialized.
2025-07-07 16:19:46.578 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-07 16:19:46.584 +08:00 [INF] Getting topics for user: llk
2025-07-07 16:19:47.095 +08:00 [DBG] info: 2025/7/7 16:19:47.095 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (6ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-07 16:19:47.158 +08:00 [INF] Getting messages for topic ID: 13
2025-07-07 16:19:47.174 +08:00 [DBG] info: 2025/7/7 16:19:47.174 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='13'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 16:19:47.197 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-07 16:20:22.015 +08:00 [INF] Application Shutting Down
2025-07-07 16:20:22.021 +08:00 [DBG] Hosting stopping
2025-07-07 16:20:22.023 +08:00 [INF] Application is shutting down...
2025-07-07 16:20:22.025 +08:00 [DBG] Hosting stopped
2025-07-07 16:23:06.213 +08:00 [DBG] Hosting starting
2025-07-07 16:23:06.278 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 16:23:06.285 +08:00 [INF] Hosting environment: Production
2025-07-07 16:23:06.287 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-07 16:23:06.288 +08:00 [DBG] Hosting started
2025-07-07 16:23:06.290 +08:00 [INF] Application Starting Up
2025-07-07 16:23:07.279 +08:00 [DBG] warn: 2025/7/7 16:23:07.279 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-07 16:23:07.443 +08:00 [DBG] info: 2025/7/7 16:23:07.443 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-07 16:23:07.448 +08:00 [INF] TopicService initialized and database ensured.
2025-07-07 16:23:07.615 +08:00 [INF] Initializing SemanticKernelService...
2025-07-07 16:23:07.635 +08:00 [INF] SemanticKernelService initialized.
2025-07-07 16:23:10.769 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-07 16:23:10.774 +08:00 [INF] Getting topics for user: llk
2025-07-07 16:23:11.279 +08:00 [DBG] info: 2025/7/7 16:23:11.279 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-07 16:23:11.340 +08:00 [INF] Getting messages for topic ID: 13
2025-07-07 16:23:11.355 +08:00 [DBG] info: 2025/7/7 16:23:11.355 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='13'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 16:23:11.378 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-07 16:26:27.494 +08:00 [INF] Application Shutting Down
2025-07-07 16:26:27.499 +08:00 [DBG] Hosting stopping
2025-07-07 16:26:27.501 +08:00 [INF] Application is shutting down...
2025-07-07 16:26:27.506 +08:00 [DBG] Hosting stopped
2025-07-07 16:26:32.763 +08:00 [DBG] Hosting starting
2025-07-07 16:26:32.828 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 16:26:32.835 +08:00 [INF] Hosting environment: Production
2025-07-07 16:26:32.838 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-07 16:26:32.839 +08:00 [DBG] Hosting started
2025-07-07 16:26:32.841 +08:00 [INF] Application Starting Up
2025-07-07 16:26:33.840 +08:00 [DBG] warn: 2025/7/7 16:26:33.839 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-07 16:26:34.011 +08:00 [DBG] info: 2025/7/7 16:26:34.011 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (14ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-07 16:26:34.016 +08:00 [INF] TopicService initialized and database ensured.
2025-07-07 16:26:34.185 +08:00 [INF] Initializing SemanticKernelService...
2025-07-07 16:26:34.205 +08:00 [INF] SemanticKernelService initialized.
2025-07-07 16:26:37.041 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-07 16:26:37.046 +08:00 [INF] Getting topics for user: llk
2025-07-07 16:26:37.551 +08:00 [DBG] info: 2025/7/7 16:26:37.551 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-07 16:26:37.612 +08:00 [INF] Getting messages for topic ID: 13
2025-07-07 16:26:37.628 +08:00 [DBG] info: 2025/7/7 16:26:37.628 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='13'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 16:26:37.652 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-07 16:26:53.890 +08:00 [INF] Application Shutting Down
2025-07-07 16:26:53.894 +08:00 [DBG] Hosting stopping
2025-07-07 16:26:53.897 +08:00 [INF] Application is shutting down...
2025-07-07 16:26:53.899 +08:00 [DBG] Hosting stopped
2025-07-07 16:27:12.793 +08:00 [DBG] Hosting starting
2025-07-07 16:27:12.853 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 16:27:12.859 +08:00 [INF] Hosting environment: Production
2025-07-07 16:27:12.861 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-07 16:27:12.862 +08:00 [DBG] Hosting started
2025-07-07 16:27:12.864 +08:00 [INF] Application Starting Up
2025-07-07 16:27:13.830 +08:00 [DBG] warn: 2025/7/7 16:27:13.829 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-07 16:27:14.001 +08:00 [DBG] info: 2025/7/7 16:27:14.001 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-07 16:27:14.006 +08:00 [INF] TopicService initialized and database ensured.
2025-07-07 16:27:14.177 +08:00 [INF] Initializing SemanticKernelService...
2025-07-07 16:27:14.197 +08:00 [INF] SemanticKernelService initialized.
2025-07-07 16:27:17.480 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-07 16:27:17.485 +08:00 [INF] Getting topics for user: llk
2025-07-07 16:27:17.988 +08:00 [DBG] info: 2025/7/7 16:27:17.988 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-07 16:27:18.047 +08:00 [INF] Getting messages for topic ID: 13
2025-07-07 16:27:18.063 +08:00 [DBG] info: 2025/7/7 16:27:18.063 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='13'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 16:27:18.085 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-07 16:27:23.330 +08:00 [INF] Getting messages for topic ID: 12
2025-07-07 16:27:23.334 +08:00 [DBG] info: 2025/7/7 16:27:23.334 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='12'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 16:27:23.338 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-07 16:27:26.727 +08:00 [INF] Getting messages for topic ID: 11
2025-07-07 16:27:26.729 +08:00 [DBG] info: 2025/7/7 16:27:26.729 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='11'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 16:27:26.732 +08:00 [INF] Setting conversation history. Message count: 5
2025-07-07 16:31:19.382 +08:00 [INF] Application Shutting Down
2025-07-07 16:31:19.385 +08:00 [DBG] Hosting stopping
2025-07-07 16:31:19.389 +08:00 [INF] Application is shutting down...
2025-07-07 16:31:19.395 +08:00 [DBG] Hosting stopped
2025-07-07 16:31:27.002 +08:00 [DBG] Hosting starting
2025-07-07 16:31:27.069 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 16:31:27.076 +08:00 [INF] Hosting environment: Production
2025-07-07 16:31:27.078 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-07 16:31:27.079 +08:00 [DBG] Hosting started
2025-07-07 16:31:27.081 +08:00 [INF] Application Starting Up
2025-07-07 16:31:28.056 +08:00 [DBG] warn: 2025/7/7 16:31:28.056 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-07 16:31:28.218 +08:00 [DBG] info: 2025/7/7 16:31:28.218 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-07 16:31:28.224 +08:00 [INF] TopicService initialized and database ensured.
2025-07-07 16:31:28.398 +08:00 [INF] Initializing SemanticKernelService...
2025-07-07 16:31:28.417 +08:00 [INF] SemanticKernelService initialized.
2025-07-07 16:31:31.527 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-07 16:31:31.532 +08:00 [INF] Getting topics for user: llk
2025-07-07 16:31:32.029 +08:00 [DBG] info: 2025/7/7 16:31:32.029 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-07 16:31:32.088 +08:00 [INF] Getting messages for topic ID: 13
2025-07-07 16:31:32.103 +08:00 [DBG] info: 2025/7/7 16:31:32.103 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='13'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 16:31:32.127 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-07 16:31:43.314 +08:00 [INF] Getting messages for topic ID: 7
2025-07-07 16:31:43.319 +08:00 [DBG] info: 2025/7/7 16:31:43.319 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 16:31:43.322 +08:00 [INF] Setting conversation history. Message count: 4
2025-07-07 16:32:19.512 +08:00 [INF] Application Shutting Down
2025-07-07 16:32:19.515 +08:00 [DBG] Hosting stopping
2025-07-07 16:32:19.521 +08:00 [INF] Application is shutting down...
2025-07-07 16:32:19.524 +08:00 [DBG] Hosting stopped
2025-07-07 16:35:56.058 +08:00 [DBG] Hosting starting
2025-07-07 16:35:56.118 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-07 16:35:56.124 +08:00 [INF] Hosting environment: Production
2025-07-07 16:35:56.126 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-07 16:35:56.128 +08:00 [DBG] Hosting started
2025-07-07 16:35:56.129 +08:00 [INF] Application Starting Up
2025-07-07 16:35:57.066 +08:00 [DBG] warn: 2025/7/7 16:35:57.065 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-07 16:35:57.227 +08:00 [DBG] info: 2025/7/7 16:35:57.227 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-07 16:35:57.232 +08:00 [INF] TopicService initialized and database ensured.
2025-07-07 16:35:57.405 +08:00 [INF] Initializing SemanticKernelService...
2025-07-07 16:35:57.426 +08:00 [INF] SemanticKernelService initialized.
2025-07-07 16:36:00.598 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-07 16:36:00.604 +08:00 [INF] Getting topics for user: llk
2025-07-07 16:36:01.116 +08:00 [DBG] info: 2025/7/7 16:36:01.116 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (6ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-07 16:36:01.181 +08:00 [INF] Getting messages for topic ID: 13
2025-07-07 16:36:01.198 +08:00 [DBG] info: 2025/7/7 16:36:01.198 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='13'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-07 16:36:01.223 +08:00 [INF] Setting conversation history. Message count: 2
