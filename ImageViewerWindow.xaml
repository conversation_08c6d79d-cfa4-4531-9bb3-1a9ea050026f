﻿<Window x:Class="Yidev.LocalAI.ImageViewerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="图片查看器"
        Height="600" Width="800"
        MinHeight="400" MinWidth="600"
        ResizeMode="CanResize"
        WindowStartupLocation="CenterScreen"
        Background="#F5F6FA"
        FontFamily="Microsoft YaHei UI, Microsoft YaHei, Segoe UI">
    
    <Window.Resources>
        <!-- 关闭按钮样式 -->
        <Style x:Key="CloseButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#FF6B6B"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Width" Value="32"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" 
                                CornerRadius="16" 
                                Background="{TemplateBinding Background}"
                                Width="32" Height="32">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FF5252" TargetName="border"/>
                                <Setter Property="LayoutTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1.1" ScaleY="1.1"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#E53935" TargetName="border"/>
                                <Setter Property="LayoutTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.95" ScaleY="0.95"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 工具栏按钮样式 -->
        <Style x:Key="ToolbarButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#4A90E2"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Width" Value="40"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" 
                                CornerRadius="20" 
                                Background="{TemplateBinding Background}"
                                Width="40" Height="40">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#357ABD" TargetName="border"/>
                                <Setter Property="LayoutTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1.1" ScaleY="1.1"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#2E6DA4" TargetName="border"/>
                                <Setter Property="LayoutTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="0.95" ScaleY="0.95"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#1976D2" Padding="15,10">
            <Grid>
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                    <Path Data="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"
                          Fill="White" 
                          Width="24" 
                          Height="24"
                          Stretch="Uniform"
                          Margin="0,0,10,0"/>
                    <TextBlock Text="图片查看器" FontSize="18" Foreground="White" FontWeight="Bold" 
                               VerticalAlignment="Center"/>
                </StackPanel>
                
                <!-- 关闭按钮 -->
                <Button x:Name="CloseButton" 
                        Style="{StaticResource CloseButtonStyle}" 
                        HorizontalAlignment="Right" 
                        VerticalAlignment="Center"
                        Click="CloseButton_Click"
                        ToolTip="关闭">
                    <Path Data="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
                          Fill="White" 
                          Stretch="Uniform" 
                          Width="16" Height="16"/>
                </Button>
            </Grid>
        </Border>

        <!-- 图片显示区域 -->
        <Border Grid.Row="1" Background="White" Margin="10">
            <Border.Effect>
                <DropShadowEffect ShadowDepth="2" BlurRadius="10" Opacity="0.2"/>
            </Border.Effect>

            <ScrollViewer x:Name="ImageScrollViewer"
                          CanContentScroll="True"
                          HorizontalScrollBarVisibility="Auto"
                          VerticalScrollBarVisibility="Auto"
                          HorizontalAlignment="Stretch"
                          VerticalAlignment="Stretch"
                          HorizontalContentAlignment="Left"
                          VerticalContentAlignment="Top"
                          Background="White">
                <Grid HorizontalAlignment="Left" VerticalAlignment="Top">
                    <!-- 图片 -->
                    <Image x:Name="MainImage"
                           Stretch="None"
                           HorizontalAlignment="Left"
                           VerticalAlignment="Top"
                           Margin="0"/>
                    
                    <!-- 加载指示器 -->
                    <StackPanel x:Name="LoadingIndicator" 
                                HorizontalAlignment="Center" 
                                VerticalAlignment="Center"
                                Visibility="Visible" Margin="350,200,0,0">
                        <Ellipse Width="50" Height="50" 
                                 Fill="#4A90E2" 
                                 Opacity="0.8">
                            <Ellipse.RenderTransform>
                                <RotateTransform x:Name="LoadingRotation"/>
                            </Ellipse.RenderTransform>
                            <Ellipse.Triggers>
                                <EventTrigger RoutedEvent="Loaded">
                                    <BeginStoryboard>
                                        <Storyboard RepeatBehavior="Forever">
                                            <DoubleAnimation Duration="0:0:1" From="0" To="360"
                                                             Storyboard.TargetName="LoadingRotation"
                                                             Storyboard.TargetProperty="Angle"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </EventTrigger>
                            </Ellipse.Triggers>
                        </Ellipse>
                        <TextBlock Text="加载中..." 
                                   FontSize="14" 
                                   Foreground="#666" 
                                   HorizontalAlignment="Center"
                                   Margin="0,10,0,0"/>
                    </StackPanel>
                    
                    <!-- 错误提示 -->
                    <StackPanel x:Name="ErrorIndicator" 
                                HorizontalAlignment="Center" 
                                VerticalAlignment="Center"
                                Visibility="Collapsed">
                        <Path Data="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
                              Fill="#FF6B6B" 
                              Width="48" 
                              Height="48"
                              Stretch="Uniform"/>
                        <TextBlock Text="图片加载失败" 
                                   FontSize="16" 
                                   Foreground="#FF6B6B" 
                                   HorizontalAlignment="Center"
                                   Margin="0,10,0,0"/>
                        <TextBlock x:Name="ErrorMessage"
                                   FontSize="12" 
                                   Foreground="#999" 
                                   HorizontalAlignment="Center"
                                   TextWrapping="Wrap"
                                   MaxWidth="300"
                                   Margin="0,5,0,0"/>
                    </StackPanel>
                </Grid>
            </ScrollViewer>
        </Border>

        <!-- 工具栏 -->
        <Border Grid.Row="2" Background="#F8F9FA" Padding="10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <!-- 缩放到适合窗口 -->
                <Button x:Name="FitToWindowButton" 
                        Style="{StaticResource ToolbarButtonStyle}"
                        Click="FitToWindow_Click"
                        ToolTip="适合窗口">
                    <Path Data="M4 4h6v2H6v4H4V4zm10 0h6v6h-2V6h-4V4zM4 14h2v4h4v2H4v-6zm16 4h-4v2h6v-6h-2v4z"
                          Fill="White" 
                          Stretch="Uniform" 
                          Width="20" Height="20"/>
                </Button>
                
                <!-- 实际大小 -->
                <Button x:Name="ActualSizeButton" 
                        Style="{StaticResource ToolbarButtonStyle}"
                        Click="ActualSize_Click"
                        ToolTip="实际大小">
                    <Path Data="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                          Fill="White" 
                          Stretch="Uniform" 
                          Width="20" Height="20"/>
                </Button>
                
                <!-- 在浏览器中打开 -->
                <Button x:Name="OpenInBrowserButton" 
                        Style="{StaticResource ToolbarButtonStyle}"
                        Click="OpenInBrowser_Click"
                        ToolTip="在浏览器中打开">
                    <Path Data="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"
                          Fill="White" 
                          Stretch="Uniform" 
                          Width="20" Height="20"/>
                </Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
