{"extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_capabilities": {"include_globs": ["https://*excel.officeapps.live.com/*", "https://*onenote.officeapps.live.com/*", "https://*powerpoint.officeapps.live.com/*", "https://*word-edit.officeapps.live.com/*", "https://*excel.officeapps.live.com.mcas.ms/*", "https://*onenote.officeapps.live.com.mcas.ms/*", "https://*word-edit.officeapps.live.com.mcas.ms/*", "https://*excel.partner.officewebapps.cn/*", "https://*onenote.partner.officewebapps.cn/*", "https://*powerpoint.partner.officewebapps.cn/*", "https://*word-edit.partner.officewebapps.cn/*", "https://*excel.gov.online.office365.us/*", "https://*onenote.gov.online.office365.us/*", "https://*powerpoint.gov.online.office365.us/*", "https://*word-edit.gov.online.office365.us/*", "https://*excel.dod.online.office365.us/*", "https://*onenote.dod.online.office365.us/*", "https://*powerpoint.dod.online.office365.us/*", "https://*word-edit.dod.online.office365.us/*", "https://*visio.partner.officewebapps.cn/*", "https://*visio.gov.online.office365.us/*", "https://*visio.dod.online.office365.us/*"], "matches": ["https://*.officeapps.live.com/*", "https://*.officeapps.live.com.mcas.ms/*", "https://*.partner.officewebapps.cn/*", "https://*.gov.online.office365.us/*", "https://*.dod.online.office365.us/*", "https://*.app.whiteboard.microsoft.com/*", "https://*.whiteboard.office.com/*", "https://*.app.int.whiteboard.microsoft.com/*", "https://*.whiteboard.office365.us/*", "https://*.dev.whiteboard.microsoft.com/*"], "permissions": ["clipboardRead", "clipboardWrite"]}, "default_locale": "en", "description": "This extension grants Microsoft web sites permission to read and write from the clipboard.", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCz4t/X7GeuP6GBpjmxndrjtzF//4CWeHlC68rkoV7hP3h5Ka6eX7ZMNlYJkSjmB5iRmPHO5kR1y7rGY8JXnRPDQh/CQNLVA7OsKeV6w+UO+vx8KGI+TrTAhzH8YGcMIsxsUjxtC4cBmprja+xDr0zVp2EMgqHu+GBKgwSRHTkDuwIDAQAB", "manifest_version": 2, "minimum_chrome_version": "77", "name": "Microsoft Clipboard Extension", "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\137.0.3296.83\\resources\\edge_clipboard", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate", "fileSystem.readFullPath", "errorReporting", "edgeLearningToolsPrivate", "fileSystem.getCurrentEntry", "edgePdfPrivate", "edgeCertVerifierPrivate"], "explicit_host": ["edge://resources/*", "edge://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:; trusted-types edge-internal fast-html pdf-url edge-pdf-static-policy;", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "edge_pdf/index.html", "name": "Microsoft Edge PDF Viewer", "offline_enabled": true, "permissions": ["errorReporting", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "edgeCertVerifierPrivate", "edgeLearningToolsPrivate", "edgePdfPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCurrentEntry"]}], "version": "1"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\137.0.3296.83\\resources\\edge_pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "protection": {"macs": {"browser": {"show_home_button": "9D77B619C2594FE237AC7DB67D34F9E72FE49CDC9C6B673538AA675D9A03F0E9"}, "default_search_provider_data": {"template_url_data": "367A3B396CF526DDDE989BC2BC8FE831EDBA4529A30F258224F1D724F9DDBE6C"}, "edge": {"services": {"account_id": "8052EC4EBBDEF9DDB86D545FCEE364A62857CAEA1A74FCFB6ADD42A83B85D3DA", "last_username": "C3ED216944465B6E7555B9204A9DB851DFBBB1A9CB0C1A7DCD62494520C3D775"}}, "enterprise_signin": {"policy_recovery_token": "C5EA4B5551BE6B15CD584D9CAAB2B13AD85107EF4E3163B37958F4A2FB091E91"}, "extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": "73BB6ED080FA3592B9EED353F29B9E3FF014BB7F273E5FCC537DB75B13AF1034", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "CF1331BEEB805E6A5D32FB23DF0F7CE0186CFBDC18EADF0C7B14609378E0A662"}, "ui": {"developer_mode": "E09BA9C365CDA09C3E990906FB286726B3F1F89F853ED331F7BA62F9A48A0CBD"}}, "google": {"services": {"last_signed_in_username": "BE3863A65B9188306372B6E554EFE22D548A3C6861D5176AE4A2360FB9558F96"}}, "homepage": "5A90F0A7B281D9FC493E372FE3E327C18626E9EEFC452FD338ED95AF27CDC9BE", "homepage_is_newtabpage": "33E18396E3DDF2D589F8215415E8E6576903A12479765444D89D8CC045243048", "media": {"cdm": {"origin_data": "FA82AB444D967327C86B651E7D9064F2BD103DC9D3CF10027517E760C4282154"}, "storage_id_salt": "303AC59EC7BF8FCBD15DF2BA95235A80B57B86559968177B8E10E6F47D5152CB"}, "pinned_tabs": "6DD5BB1D8CBC68072B313287615876867DD95E5C82D3DE3FFC69E68324BD06DC", "prefs": {"preference_reset_time": "DCD9BBF3F28F81DA1B364BCE497D7C90A77E8FEFB8AE58C18D8C0B31526DA8C0"}, "safebrowsing": {"incidents_sent": "531C52EE0D29276E9EDAA1C344169EFF09EAE7A97AC997F8FFFAE9A6CF03ADEC"}, "search_provider_overrides": "4A0366FDC168D4E004F20FB997D6A5970447119331D266584A09538C5C070FCE", "session": {"restore_on_startup": "4BE7D80FE85433B5C566BED2CE331A5EACC62A89BD4ADAC6EF08F23A4A839AF5", "startup_urls": "97FDA44FF6DC2A32FD94B650212768EF7FDB0A2B71D638A80CF5E56A013FC609"}}, "super_mac": "843DCFD7D1EF7ACE738D6383C2E48AE5DB295D48B07F50212DF180389E8F4A7F"}}