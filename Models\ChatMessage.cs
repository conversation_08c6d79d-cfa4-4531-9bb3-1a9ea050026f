﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace Yidev.LocalAI.Models
{
    public class ChatMessage
    {
        public int Id { get; set; }
        public string Role { get; set; }
        public string Content { get; set; }
        public DateTime Timestamp { get; set; }
        public int TopicId { get; set; }
        public Topic Topic { get; set; }
        public string? SenderName { get; set; }
        [NotMapped]
        public string Avatar { get; set; }

        /// <summary>
        /// 消息中包含的图片URL列表，用于显示图片内容
        /// </summary>
        [NotMapped]
        public List<string> ImageUrls { get; set; } = new List<string>();

        /// <summary>
        /// 判断消息是否包含图片
        /// </summary>
        [NotMapped]
        public bool HasImages => ImageUrls?.Any() == true;
    }
}
