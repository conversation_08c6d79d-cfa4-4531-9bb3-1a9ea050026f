using System;
using System.Threading.Tasks;
using Yidev.LocalAI.Services;

namespace Yidev.LocalAI
{
    /// <summary>
    /// 简单的测试类，用于验证话题标题生成功能
    /// </summary>
    public class TestTopicTitleGeneration
    {
        public static async Task TestGenerateTopicTitle()
        {
            Console.WriteLine("开始测试话题标题生成功能...");
            
            try
            {
                // 创建 SemanticKernelService 实例
                var semanticKernelService = await SemanticKernelService.CreateAsync();
                
                // 测试用例
                string[] testMessages = {
                    "你好，请介绍一下你自己",
                    "如何学习编程？",
                    "什么是人工智能？",
                    "请帮我写一个Python函数来计算斐波那契数列",
                    "今天天气怎么样？",
                    "能否解释一下机器学习和深度学习的区别？"
                };
                
                Console.WriteLine("测试消息和生成的标题：");
                Console.WriteLine(new string('=', 50));

                foreach (var message in testMessages)
                {
                    try
                    {
                        var title = await semanticKernelService.GenerateTopicTitleAsync(message);
                        Console.WriteLine($"消息: {message}");
                        Console.WriteLine($"标题: {title}");
                        Console.WriteLine(new string('-', 30));
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"生成标题失败: {message}");
                        Console.WriteLine($"错误: {ex.Message}");
                        Console.WriteLine(new string('-', 30));
                    }
                }
                
                Console.WriteLine("测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细信息: {ex}");
            }
        }
    }
}
