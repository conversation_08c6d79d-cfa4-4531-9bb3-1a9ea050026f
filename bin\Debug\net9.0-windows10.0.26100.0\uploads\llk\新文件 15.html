// ... existing code ...
@section Scripts {
    <!-- 添加Marked.js库用于Markdown转换 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    
    <script>
        $(document).ready(function () {
            // 配置marked选项
            marked.setOptions({
                breaks: true,  // 支持GitHub风格的换行
                gfm: true,     // 启用GitHub风格的Markdown
                highlight: function(code, lang) {
                    // 如果有语法高亮库如Prism或Highlight.js，可以在这里配置
                    return code;
                }
            });
            
            // ... existing code ...
                
            // 发送消息的AJAX处理
            $('#chatForm').submit(function (e) {
                e.preventDefault();

                var userMessage = $('#userMessage').val().trim();
                var fileInput = $('#fileUpload')[0];
                var topicId = $('#topicId').val();

                if (!userMessage && !fileInput.files.length) {
                    alert('请输入消息或上传文件');
                    return;
                }

                // 创建FormData对象用于文件上传
                var formData = new FormData();
                formData.append('userMessage', userMessage);
                formData.append('topicId', topicId);

                if (fileInput.files.length > 0) {
                    formData.append('uploadedFile', fileInput.files[0]);
                }

                // 添加用户消息到聊天界面
                var userMessageHtml = `
                    <div class="message user-message">
                        <div class="message-header">
                            <img src="@User.FindFirst("avatar")?.Value" width="40" height="40"/>
                            <small class="text-muted">${new Date().toLocaleString()}</small>
                            <div class="message-actions">
                                <button class="btn btn-sm btn-link edit-message" title="编辑" data-message-id="">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-link copy-message" title="复制" data-message-id="">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="btn btn-sm btn-link delete-message" title="删除" data-message-id="">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <div class="message-content">${userMessage}</div>
                `;
                // 如果有文件，添加文件附件指示
                if (fileInput.files.length > 0) {
                    userMessageHtml += `
                        <div class="message-attachment mt-2">
                            <span class="btn btn-sm btn-outline-secondary disabled">
                                <i class="fas fa-paperclip"></i> ${fileInput.files[0].name}
                            </span>
                        </div>
                    `;
                }

                userMessageHtml += `</div>`;
                $('#chatContainer').append(userMessageHtml);
                scrollToBottom();

                // 清空输入框和文件选择
                $('#userMessage').val('');
                $('#fileUpload').val('');
                $('#selectedFileName').text('');

                // 显示正在输入指示器
                $('#typingIndicator').removeClass('d-none');
                
                // 启动自动保存计时器
                startAutoSave();

                // 发送AJAX请求
                $.ajax({
                    url: '@Url.Action("SendMessage", "Excel")',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        // 隐藏正在输入指示器
                        $('#typingIndicator').addClass('d-none');

                        // 使用marked将Markdown转换为HTML
                        const formattedResponse = marked.parse(response.aiResponse);

                        // 添加AI回复到聊天界面
                        var aiMessageHtml = `
                            <div class="message ai-message">
                                <div class="message-header">
                                    <img src="https://home.yidev.cn/images/uni-ai.png" width="40" height="40"/>
                                    <!--<strong>AI 助手</strong>-->
                                    <small class="text-muted">${new Date().toLocaleString()}</small>
                                    <div class="message-actions">
                                        <button class="btn btn-sm btn-link copy-message" title="复制" data-message-id="${response.aiMessageId || ''}">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                        <button class="btn btn-sm btn-link delete-message" title="删除" data-message-id="${response.aiMessageId || ''}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <button class="btn btn-sm btn-link regenerate-message" title="重新生成" data-message-id="${response.aiMessageId || ''}">
                                            <i class="fas fa-sync-alt"></i>
                                        </button>
                                        <button class="btn btn-sm btn-link delete-regenerate-message" title="删除并重新生成" data-message-id="${response.aiMessageId || ''}">
                                            <i class="fas fa-redo-alt"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="message-content markdown-content">${formattedResponse}</div>
                        `;
                        // 如果AI回复包含附件链接
                        if (response.attachmentUrl) {
                            aiMessageHtml += `
                                <div class="message-attachment mt-2">
                                    <a href="${response.attachmentUrl}" target="_blank" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-paperclip"></i> 查看附件
                                    </a>
                                </div>
                            `;
                        }

                        aiMessageHtml += `</div>`;
                        $('#chatContainer').append(aiMessageHtml);
                        scrollToBottom();
                        
                        // 如果话题ID已更新，更新页面上的话题ID
                        if (response.topicId && response.topicId !== topicId) {
                            $('#topicId').val(response.topicId);
                            
                            // 如果是自动创建的话题，更新UI
                            if (response.topicName) {
                                $('#currentTopicName').text(response.topicName);
                                $('#saveTopic').prop('disabled', true);
                                $('#renameTopic').prop('disabled', false);
                                
                                // 停止自动保存
                                stopAutoSave();
                                
                                // 刷新话题列表
                                loadTopics();
                            }
                        }
                    },
                    // ... existing code ...
                });
            });

            // ... existing code ...

            // 重新生成消息函数
            function regenerateMessage(messageId, deleteOriginal) {
                $('#typingIndicator').removeClass('d-none');

                $.ajax({
                    url: '@Url.Action("RegenerateMessage", "Excel")',
                    type: 'POST',
                    data: {
                        messageId: messageId,
                        deleteOriginal: deleteOriginal
                    },
                    success: function(response) {
                        $('#typingIndicator').addClass('d-none');

                        if (response.success) {
                            // 如果是删除并重新生成，先删除原消息
                            if (deleteOriginal) {
                                $(`.message .delete-message[data-message-id="${messageId}"]`).closest('.message').remove();
                            }

                            // 使用marked将Markdown转换为HTML
                            const formattedResponse = marked.parse(response.aiResponse);

                            // 添加新生成的消息
                            var aiMessageHtml = `
                                <div class="message ai-message">
                                    <div class="message-header">
                                        <img src="https://home.yidev.cn/images/uni-ai.png" width="40" height="40"/>
                                        <small class="text-muted">${new Date().toLocaleString()}</small>
                                        <div class="message-actions">
                                            <button class="btn btn-sm btn-link copy-message" title="复制" data-message-id="${response.aiMessageId || ''}">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                            <button class="btn btn-sm btn-link delete-message" title="删除" data-message-id="${response.aiMessageId || ''}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <button class="btn btn-sm btn-link regenerate-message" title="重新生成" data-message-id="${response.aiMessageId || ''}">
                                                <i class="fas fa-sync-alt"></i>
                                            </button>
                                            <button class="btn btn-sm btn-link delete-regenerate-message" title="删除并重新生成" data-message-id="${response.aiMessageId || ''}">
                                                <i class="fas fa-redo-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="message-content markdown-content">${formattedResponse}</div>
                            `;

                            if (response.attachmentUrl) {
                                aiMessageHtml += `
                                    <div class="message-attachment mt-2">
                                        <a href="${response.attachmentUrl}" target="_blank" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-paperclip"></i> 查看附件
                                        </a>
                                    </div>
                                `;
                            }

                            aiMessageHtml += `</div>`;
                            $('#chatContainer').append(aiMessageHtml);
                            scrollToBottom();
                        } else {
                            alert('重新生成消息失败: ' + response.message);
                        }
                    },
                    // ... existing code ...
                });
            }

            // ... existing code ...

            // 加载指定话题
            function loadTopic(topicId) {
                $.ajax({
                    url: '@Url.Action("LoadTopic", "Excel")',
                    type: 'GET',
                    data: { topicId: topicId },
                    success: function(response) {
                        if (response.success) {
                            // 更新话题ID和名称
                            $('#topicId').val(response.topicId);
                            $('#currentTopicName').text(response.topicName);
                            $('#saveTopic').prop('disabled', true);
                            $('#renameTopic').prop('disabled', false);
                            
                            // 停止自动保存
                            stopAutoSave();
                            
                            // 清空并重建聊天历史
                            $('#chatContainer').empty();
                            
                            if (response.chatHistory && response.chatHistory.length > 0) {
                                response.chatHistory.forEach(function(message) {
                                    // 对AI消息使用Markdown解析
                                    let messageContent = message.isUserMessage ? 
                                        message.content : 
                                        marked.parse(message.content);
                                
                                    var messageHtml = `
                                        <div class="message ${message.isUserMessage ? 'user-message' : 'ai-message'}">
                                            <div class="message-header">
                                                ${message.isUserMessage ? `
                                                    <img src="@User.FindFirst("avatar")?.Value" width="40" height="40"/>
                                                    <small class="text-muted">${new Date(message.timestamp).toLocaleString()}</small>
                                                    <div class="message-actions">
                                                        <button class="btn btn-sm btn-link edit-message" title="编辑" data-message-id="${message.id}">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-link copy-message" title="复制" data-message-id="${message.id}">
                                                            <i class="fas fa-copy"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-link delete-message" title="删除" data-message-id="${message.id}">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                ` : `
                                                    <img src="https://home.yidev.cn/images/uni-ai.png" width="40" height="40"/>
                                                    <!--<strong>AI 助手</strong>-->
                                                    <small class="text-muted">${new Date(message.timestamp).toLocaleString()}</small>
                                                    <div class="message-actions">
                                                        <button class="btn btn-sm btn-link copy-message" title="复制" data-message-id="${message.id}">
                                                            <i class="fas fa-copy"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-link delete-message" title="删除" data-message-id="${message.id}">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-link regenerate-message" title="重新生成" data-message-id="${message.id}">
                                                            <i class="fas fa-sync-alt"></i>
                                                        </button>
                                                        <button class="btn btn-sm btn-link delete-regenerate-message" title="删除并重新生成" data-message-id="${message.id}">
                                                            <i class="fas fa-redo-alt"></i>
                                                        </button>
                                                    </div>
                                                `}
                                            </div>
                                            <div class="message-content ${!message.isUserMessage ? 'markdown-content' : ''}">${messageContent}</div>
                                    `;
                                    // ... existing code ...
                                });
                                
                                scrollToBottom();
                            } else {
                                // ... existing code ...
                            }
                            
                            // ... existing code ...
                        } else {
                            alert('加载话题失败: ' + response.message);
                        }
                    },
                    error: function() {
                        alert('加载话题失败，请稍后再试。');
                    }
                });
            }
            
            // ... existing code ...
        });
    </script>
}
