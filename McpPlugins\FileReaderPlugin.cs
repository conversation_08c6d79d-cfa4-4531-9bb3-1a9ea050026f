﻿using System;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Security;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;

namespace Yidev.LocalAI.McpPlugins // 确保命名空间正确
{
    public class FileReaderPlugin
    {
        private readonly ILogger<FileReaderPlugin> _logger;
        private readonly string _baseUploadDirectory;
        private readonly long _maxReadSizeBytes = 10 * 1024 * 1024; // 限制读取文件大小为 10MB
        private readonly string[] _allowedTextExtensions = { ".txt", ".csv", ".md", ".json", ".xml", ".html", ".css", ".js" }; // 允许直接读取文本内容的扩展名

        public FileReaderPlugin(
            ILogger<FileReaderPlugin> logger)
        {
            _logger = logger;
            _baseUploadDirectory = Path.Combine(AppContext.BaseDirectory, "uploads");
        }

        // 辅助方法：获取当前用户的上传目录（需要 HttpContext）
        private string GetUserUploadDirectory()
        {
            var viewModel = ViewModelLocator.MainViewModelInstance; // Use shared instance
            var userDirectory = Path.Combine(_baseUploadDirectory, viewModel.Username);

            // 确保用户目录存在 (可选，取决于你的应用逻辑，读取时不一定需要创建)
            // if (!Directory.Exists(userDirectory))
            // {
            //     Directory.CreateDirectory(userDirectory);
            // }
            return userDirectory;
        }

        [KernelFunction,
         Description("读取指定文件的文本内容。当用户明确要求查看某个文件的内容，或者你需要一个文件的具体文本信息来回答用户的问题或完成用户的请求时，应调用此工具。此工具优先尝试读取纯文本文件。如果文件是 Excel、PDF 或 Word 等特殊格式，它会返回提示信息而不是原始二进制内容。文件路径必须是用户授权上传目录下的有效路径。")]
        public async Task<string> ReadFileContentAsync(
            [Description("要读取的文件的完整绝对路径，该路径必须位于当前用户的授权上传目录内。例如：'my_document.txt' 或 'project_data/notes.md'。请提供精确的文件名和相对路径（如果文件在子目录中），工具会将其解析为用户上传目录下的完整路径。")] string filePath
        )
        {
            string userDirectory;
            try
            {
                userDirectory = GetUserUploadDirectory();
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError(ex, "无法获取用户目录以读取文件。");
                return $"错误：无法确定用户目录。 {ex.Message}";
            }
            catch (Exception ex) // 捕获其他潜在错误
            {
                _logger.LogError(ex, "获取用户目录时发生意外错误。");
                return $"错误：获取用户目录时发生意外错误。 {ex.Message}";
            }


            _logger.LogInformation("尝试读取文件: {FilePath}，用户目录: {UserDirectory}", filePath, userDirectory);

            try
            {
                // --- 安全性检查 ---
                // 1. 规范化路径
                var fullPath = Path.GetFullPath(filePath); // 解析 ".." 等，获取绝对路径
                var fullUserDirectory = Path.GetFullPath(userDirectory); // 确保用户目录也是绝对且规范的

                // 2. 验证文件是否在用户目录下 (防止路径遍历攻击)
                //    确保 fullPath 是以 fullUserDirectory 开头的，并且后面紧跟路径分隔符
                if (!fullPath.StartsWith(fullUserDirectory + Path.DirectorySeparatorChar) && fullPath != fullUserDirectory) // 允许用户目录本身？可能不需要
                {
                    _logger.LogWarning("拒绝访问：请求的文件路径 '{FullPath}' 不在授权的用户目录 '{UserDirectory}' 内。", fullPath, fullUserDirectory);
                    return $"错误：无权访问指定路径的文件。路径必须在 '{userDirectory}' 目录下。";
                }

                // 3. 检查文件是否存在
                if (!File.Exists(fullPath))
                {
                    _logger.LogWarning("文件未找到: {FullPath}", fullPath);
                    return $"错误：文件未找到 '{Path.GetFileName(filePath)}'。请确认文件存在于 '{userDirectory}' 目录下且路径正确。";
                }

                // 4. 检查文件大小
                var fileInfo = new FileInfo(fullPath);
                if (fileInfo.Length > _maxReadSizeBytes)
                {
                    _logger.LogWarning("文件过大: {FullPath} ({Length} bytes)", fullPath, fileInfo.Length);
                    return $"错误：文件 '{Path.GetFileName(filePath)}' 过大 (超过 {_maxReadSizeBytes / 1024 / 1024}MB)，无法直接读取。";
                }

                // --- 文件类型处理 ---
                var fileExtension = Path.GetExtension(fullPath).ToLowerInvariant();

                if (_allowedTextExtensions.Contains(fileExtension))
                {
                    // 读取允许的文本文件
                    var content = await File.ReadAllTextAsync(fullPath);
                    _logger.LogInformation("成功读取文本文件: {FullPath}", fullPath);
                    // 可以考虑截断过长的内容
                    const int maxContentLength = 100000; // 限制返回给AI的内容长度
                    if (content.Length > maxContentLength)
                    {
                        _logger.LogWarning("文件内容过长，已截断: {FullPath}", fullPath);
                        return content.Substring(0, maxContentLength) + "\n\n[内容过长，已被截断]";
                    }
                    return content;
                }
                else if (fileExtension == ".xlsx" || fileExtension == ".xls")
                {
                    // 对于 Excel 文件，告知 AI 需要使用特定工具
                    _logger.LogInformation("请求读取 Excel 文件，建议使用 excel-mcp-server: {FullPath}", fullPath);
                    return $"提示：这是一个 Excel 文件 ('{Path.GetFileName(filePath)}')。如需分析或提取数据，请考虑使用 'excel-mcp-server' 工具。直接读取原始格式可能无法获得有用信息。";
                }
                else if (fileExtension == ".pdf" || fileExtension == ".doc" || fileExtension == ".docx")
                {
                    // 对于 PDF/Word，告知无法直接读取
                    _logger.LogInformation("请求读取不支持的二进制文件类型: {FullPath}", fullPath);
                    return $"错误：无法直接读取 '{Path.GetFileName(filePath)}' ({fileExtension}) 类型的文件内容。";
                }
                else
                {
                    // 其他未知或不允许的文件类型
                    _logger.LogWarning("不支持读取的文件类型: {FullPath}", fullPath);
                    return $"错误：不支持直接读取 '{Path.GetFileName(filePath)}' ({fileExtension}) 类型的文件。";
                }
            }
            catch (SecurityException ex)
            {
                _logger.LogError(ex, "读取文件时发生安全错误: {FilePath}", filePath);
                return $"错误：读取文件 '{Path.GetFileName(filePath)}' 时权限不足。";
            }
            catch (IOException ex)
            {
                _logger.LogError(ex, "读取文件时发生 IO 错误: {FilePath}", filePath);
                return $"错误：读取文件 '{Path.GetFileName(filePath)}' 时发生 IO 错误: {ex.Message}";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "读取文件时发生意外错误: {FilePath}", filePath);
                return $"错误：读取文件 '{Path.GetFileName(filePath)}' 时发生未知错误。";
            }
        }

        [KernelFunction,
         Description("列出指定目录下的所有文件和子目录。你可以用它来回答用户关于'当前有哪些文件'、'请列出目录内容'等问题。目录路径必须在用户上传目录下。")]
        public string ListFiles(
            [Description("要列出的目录的完整绝对路径，必须在用户上传目录下。例如：'/path/to/userdir'。若用户未指定，默认用你的根目录。")]
            string directoryPath)
        {
            string userDirectory;
            try
            {
                userDirectory = GetUserUploadDirectory();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "无法获取用户目录以列出文件。");
                return $"错误：无法确定用户目录。 {ex.Message}";
            }

            _logger.LogInformation("尝试列出目录: {DirectoryPath}，用户目录: {UserDirectory}", directoryPath, userDirectory);

            try
            {
                var fullPath = Path.GetFullPath(directoryPath);
                var fullUserDirectory = Path.GetFullPath(userDirectory);

                if (!fullPath.StartsWith(fullUserDirectory + Path.DirectorySeparatorChar) && fullPath != fullUserDirectory)
                {
                    _logger.LogWarning("拒绝访问：请求的目录路径 '{FullPath}' 不在授权的用户目录 '{UserDirectory}' 内。", fullPath, fullUserDirectory);
                    return $"错误：无权访问指定路径的目录。路径必须在 '{userDirectory}' 目录下。";
                }

                if (!Directory.Exists(fullPath))
                {
                    _logger.LogWarning("目录未找到: {FullPath}", fullPath);
                    return $"错误：目录未找到 '{directoryPath}'。";
                }

                var entries = Directory.GetFileSystemEntries(fullPath);
                if (entries.Length == 0) return "该目录为空。";
                return string.Join("\n", entries.Select(Path.GetFileName));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "列出目录时发生错误: {DirectoryPath}", directoryPath);
                return $"错误：列出目录 '{directoryPath}' 时发生错误：{ex.Message}";
            }
        }
    }
}