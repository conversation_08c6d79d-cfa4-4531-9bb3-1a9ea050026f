2025-06-25 10:50:33.604 +08:00 [INF] Application Shutting Down
2025-06-25 10:50:37.480 +08:00 [INF] Application Starting Up
2025-06-25 10:50:42.005 +08:00 [DBG] warn: 2025/6/25 10:50:42.005 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-25 10:50:42.186 +08:00 [DBG] info: 2025/6/25 10:50:42.186 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (27ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-25 10:50:42.191 +08:00 [INF] TopicService initialized and database ensured.
2025-06-25 10:50:42.985 +08:00 [INF] Initializing SemanticKernelService...
2025-06-25 10:50:43.432 +08:00 [INF] SemanticKernelService initialized.
2025-06-25 10:50:46.726 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-25 10:50:46.732 +08:00 [INF] Getting topics for user: llk
2025-06-25 10:50:47.223 +08:00 [DBG] info: 2025/6/25 10:50:47.223 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-25 10:50:47.282 +08:00 [INF] Getting messages for topic ID: 7
2025-06-25 10:50:47.296 +08:00 [DBG] info: 2025/6/25 10:50:47.296 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-25 10:50:47.318 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-25 10:52:55.323 +08:00 [INF] Application Shutting Down
2025-06-25 10:53:22.562 +08:00 [INF] Application Starting Up
2025-06-25 10:53:23.634 +08:00 [DBG] warn: 2025/6/25 10:53:23.634 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-25 10:53:23.804 +08:00 [DBG] info: 2025/6/25 10:53:23.804 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-25 10:53:23.810 +08:00 [INF] TopicService initialized and database ensured.
2025-06-25 10:53:23.986 +08:00 [INF] Initializing SemanticKernelService...
2025-06-25 10:53:24.005 +08:00 [INF] SemanticKernelService initialized.
2025-06-25 10:53:26.709 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-25 10:53:26.714 +08:00 [INF] Getting topics for user: llk
2025-06-25 10:53:27.219 +08:00 [DBG] info: 2025/6/25 10:53:27.219 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (6ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-25 10:53:27.284 +08:00 [INF] Getting messages for topic ID: 7
2025-06-25 10:53:27.299 +08:00 [DBG] info: 2025/6/25 10:53:27.299 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-25 10:53:27.321 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-25 10:53:44.933 +08:00 [INF] Application Shutting Down
2025-06-25 10:56:47.787 +08:00 [INF] Application Starting Up
2025-06-25 10:56:48.821 +08:00 [DBG] warn: 2025/6/25 10:56:48.820 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-25 10:56:48.989 +08:00 [DBG] info: 2025/6/25 10:56:48.989 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-25 10:56:48.994 +08:00 [INF] TopicService initialized and database ensured.
2025-06-25 10:56:49.160 +08:00 [INF] Initializing SemanticKernelService...
2025-06-25 10:56:49.180 +08:00 [INF] SemanticKernelService initialized.
2025-06-25 10:56:51.873 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-25 10:56:51.879 +08:00 [INF] Getting topics for user: llk
2025-06-25 10:56:52.385 +08:00 [DBG] info: 2025/6/25 10:56:52.385 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-25 10:56:52.444 +08:00 [INF] Getting messages for topic ID: 7
2025-06-25 10:56:52.459 +08:00 [DBG] info: 2025/6/25 10:56:52.459 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-25 10:56:52.481 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-25 11:00:01.455 +08:00 [INF] Application Shutting Down
2025-06-25 11:18:42.271 +08:00 [DBG] Hosting starting
2025-06-25 11:18:42.331 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-25 11:18:42.338 +08:00 [INF] Hosting environment: Production
2025-06-25 11:18:42.340 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-06-25 11:18:42.342 +08:00 [DBG] Hosting started
2025-06-25 11:18:42.343 +08:00 [INF] Application Starting Up
2025-06-25 11:18:43.349 +08:00 [DBG] warn: 2025/6/25 11:18:43.349 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-25 11:18:43.514 +08:00 [DBG] info: 2025/6/25 11:18:43.514 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-25 11:18:43.519 +08:00 [INF] TopicService initialized and database ensured.
2025-06-25 11:18:43.693 +08:00 [INF] Initializing SemanticKernelService...
2025-06-25 11:18:43.713 +08:00 [INF] SemanticKernelService initialized.
2025-06-25 11:18:43.917 +08:00 [DBG] info: 2025/6/25 11:18:43.917 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-25 11:18:43.918 +08:00 [INF] TopicService initialized and database ensured.
2025-06-25 11:18:44.114 +08:00 [INF] Initializing SemanticKernelService...
2025-06-25 11:18:44.116 +08:00 [INF] SemanticKernelService initialized.
2025-06-25 11:18:46.536 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-25 11:18:46.541 +08:00 [INF] Getting topics for user: llk
2025-06-25 11:18:47.052 +08:00 [DBG] info: 2025/6/25 11:18:47.052 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-25 11:18:47.112 +08:00 [INF] Getting messages for topic ID: 7
2025-06-25 11:18:47.127 +08:00 [DBG] info: 2025/6/25 11:18:47.127 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-25 11:18:47.149 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-25 11:18:47.651 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-25 11:18:47.652 +08:00 [INF] Getting topics for user: llk
2025-06-25 11:18:47.657 +08:00 [DBG] info: 2025/6/25 11:18:47.657 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-25 11:18:47.660 +08:00 [INF] Getting messages for topic ID: 7
2025-06-25 11:18:47.663 +08:00 [DBG] info: 2025/6/25 11:18:47.663 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-25 11:18:47.665 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-25 11:26:36.622 +08:00 [INF] Application Shutting Down
2025-06-25 11:26:36.626 +08:00 [DBG] Hosting stopping
2025-06-25 11:26:36.628 +08:00 [INF] Application is shutting down...
2025-06-25 11:26:36.630 +08:00 [DBG] Hosting stopped
2025-06-25 11:36:11.322 +08:00 [DBG] Hosting starting
2025-06-25 11:36:11.387 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-25 11:36:11.394 +08:00 [INF] Hosting environment: Production
2025-06-25 11:36:11.397 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-06-25 11:36:11.398 +08:00 [DBG] Hosting started
2025-06-25 11:36:11.400 +08:00 [INF] Application Starting Up
2025-06-25 11:36:12.419 +08:00 [DBG] warn: 2025/6/25 11:36:12.419 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-25 11:36:12.596 +08:00 [DBG] info: 2025/6/25 11:36:12.596 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-25 11:36:12.602 +08:00 [INF] TopicService initialized and database ensured.
2025-06-25 11:36:12.788 +08:00 [INF] Initializing SemanticKernelService...
2025-06-25 11:36:12.809 +08:00 [INF] SemanticKernelService initialized.
2025-06-25 11:36:13.020 +08:00 [DBG] info: 2025/6/25 11:36:13.020 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-25 11:36:13.021 +08:00 [INF] TopicService initialized and database ensured.
2025-06-25 11:36:13.230 +08:00 [INF] Initializing SemanticKernelService...
2025-06-25 11:36:13.231 +08:00 [INF] SemanticKernelService initialized.
2025-06-25 11:36:15.534 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-25 11:36:15.539 +08:00 [INF] Getting topics for user: llk
2025-06-25 11:36:16.040 +08:00 [DBG] info: 2025/6/25 11:36:16.040 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-25 11:36:16.101 +08:00 [INF] Getting messages for topic ID: 7
2025-06-25 11:36:16.116 +08:00 [DBG] info: 2025/6/25 11:36:16.116 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-25 11:36:16.139 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-25 11:36:16.214 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-25 11:36:16.215 +08:00 [INF] Getting topics for user: llk
2025-06-25 11:36:16.220 +08:00 [DBG] info: 2025/6/25 11:36:16.220 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-25 11:36:16.223 +08:00 [INF] Getting messages for topic ID: 7
2025-06-25 11:36:16.226 +08:00 [DBG] info: 2025/6/25 11:36:16.226 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-25 11:36:16.228 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-25 15:46:31.944 +08:00 [INF] Application Shutting Down
2025-06-25 15:46:31.948 +08:00 [DBG] Hosting stopping
2025-06-25 15:46:31.950 +08:00 [INF] Application is shutting down...
2025-06-25 15:46:31.954 +08:00 [DBG] Hosting stopped
2025-06-25 15:49:40.612 +08:00 [DBG] Hosting starting
2025-06-25 15:49:40.674 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-25 15:49:40.681 +08:00 [INF] Hosting environment: Production
2025-06-25 15:49:40.683 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-06-25 15:49:40.685 +08:00 [DBG] Hosting started
2025-06-25 15:49:40.687 +08:00 [INF] Application Starting Up
2025-06-25 15:49:41.670 +08:00 [DBG] warn: 2025/6/25 15:49:41.669 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-25 15:49:41.836 +08:00 [DBG] info: 2025/6/25 15:49:41.835 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-25 15:49:41.841 +08:00 [INF] TopicService initialized and database ensured.
2025-06-25 15:49:42.013 +08:00 [INF] Initializing SemanticKernelService...
2025-06-25 15:49:42.033 +08:00 [INF] SemanticKernelService initialized.
2025-06-25 15:49:42.243 +08:00 [DBG] info: 2025/6/25 15:49:42.243 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-25 15:49:42.245 +08:00 [INF] TopicService initialized and database ensured.
2025-06-25 15:49:42.457 +08:00 [INF] Initializing SemanticKernelService...
2025-06-25 15:49:42.458 +08:00 [INF] SemanticKernelService initialized.
2025-06-25 15:49:45.298 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-25 15:49:45.304 +08:00 [INF] Getting topics for user: llk
2025-06-25 15:49:45.847 +08:00 [DBG] info: 2025/6/25 15:49:45.847 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-25 15:49:45.910 +08:00 [INF] Getting messages for topic ID: 7
2025-06-25 15:49:45.927 +08:00 [DBG] info: 2025/6/25 15:49:45.927 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-25 15:49:45.951 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-25 15:49:46.027 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-25 15:49:46.029 +08:00 [INF] Getting topics for user: llk
2025-06-25 15:49:46.033 +08:00 [DBG] info: 2025/6/25 15:49:46.033 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-25 15:49:46.036 +08:00 [INF] Getting messages for topic ID: 7
2025-06-25 15:49:46.039 +08:00 [DBG] info: 2025/6/25 15:49:46.039 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-25 15:49:46.042 +08:00 [INF] Setting conversation history. Message count: 4
