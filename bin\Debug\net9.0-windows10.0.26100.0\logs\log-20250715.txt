2025-07-15 10:57:59.141 +08:00 [INF] Application Shutting Down
2025-07-15 10:57:59.147 +08:00 [DBG] Hosting stopping
2025-07-15 10:57:59.153 +08:00 [INF] Application is shutting down...
2025-07-15 10:57:59.156 +08:00 [DBG] Hosting stopped
2025-07-15 10:58:06.215 +08:00 [DBG] Hosting starting
2025-07-15 10:58:06.303 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 10:58:06.309 +08:00 [INF] Hosting environment: Production
2025-07-15 10:58:06.312 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-15 10:58:06.313 +08:00 [DBG] Hosting started
2025-07-15 10:58:06.315 +08:00 [INF] Application Starting Up
2025-07-15 10:58:09.903 +08:00 [DBG] warn: 2025/7/15 10:58:09.903 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-15 10:58:10.069 +08:00 [DBG] info: 2025/7/15 10:58:10.068 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (19ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-15 10:58:10.073 +08:00 [INF] TopicService initialized and database ensured.
2025-07-15 10:58:10.711 +08:00 [INF] Initializing SemanticKernelService...
2025-07-15 10:58:11.054 +08:00 [INF] SemanticKernelService initialized.
2025-07-15 10:58:16.441 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-15 10:58:20.925 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-15 10:58:21.114 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-15 10:58:21.119 +08:00 [INF] Getting topics for user: llk
2025-07-15 10:58:21.608 +08:00 [DBG] info: 2025/7/15 10:58:21.608 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-15 10:58:21.666 +08:00 [INF] Getting messages for topic ID: 35
2025-07-15 10:58:21.680 +08:00 [DBG] info: 2025/7/15 10:58:21.680 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='35'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-15 10:58:21.706 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-15 10:58:21.707 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-15 11:04:02.332 +08:00 [INF] Application Shutting Down
2025-07-15 11:04:02.336 +08:00 [DBG] Hosting stopping
2025-07-15 11:04:02.338 +08:00 [INF] Application is shutting down...
2025-07-15 11:04:02.341 +08:00 [DBG] Hosting stopped
2025-07-15 11:04:22.882 +08:00 [DBG] Hosting starting
2025-07-15 11:04:22.944 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 11:04:22.950 +08:00 [INF] Hosting environment: Production
2025-07-15 11:04:22.952 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-15 11:04:22.954 +08:00 [DBG] Hosting started
2025-07-15 11:04:22.955 +08:00 [INF] Application Starting Up
2025-07-15 11:04:23.939 +08:00 [DBG] warn: 2025/7/15 11:04:23.939 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-15 11:04:24.096 +08:00 [DBG] info: 2025/7/15 11:04:24.096 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (10ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-15 11:04:24.102 +08:00 [INF] TopicService initialized and database ensured.
2025-07-15 11:04:24.264 +08:00 [INF] Initializing SemanticKernelService...
2025-07-15 11:04:24.285 +08:00 [INF] SemanticKernelService initialized.
2025-07-15 11:04:27.438 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-15 11:04:31.276 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-15 11:04:31.396 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-15 11:04:31.401 +08:00 [INF] Getting topics for user: llk
2025-07-15 11:04:31.912 +08:00 [DBG] info: 2025/7/15 11:04:31.912 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-15 11:04:31.972 +08:00 [INF] Getting messages for topic ID: 35
2025-07-15 11:04:31.987 +08:00 [DBG] info: 2025/7/15 11:04:31.987 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='35'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-15 11:04:32.014 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-15 11:04:32.016 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-15 11:11:49.182 +08:00 [INF] Application Shutting Down
2025-07-15 11:11:49.186 +08:00 [DBG] Hosting stopping
2025-07-15 11:11:49.189 +08:00 [INF] Application is shutting down...
2025-07-15 11:11:49.194 +08:00 [DBG] Hosting stopped
2025-07-15 11:19:43.851 +08:00 [DBG] Hosting starting
2025-07-15 11:19:43.912 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 11:19:43.918 +08:00 [INF] Hosting environment: Production
2025-07-15 11:19:43.920 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-15 11:19:43.922 +08:00 [DBG] Hosting started
2025-07-15 11:19:43.923 +08:00 [INF] Application Starting Up
2025-07-15 11:19:44.904 +08:00 [DBG] warn: 2025/7/15 11:19:44.904 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-15 11:19:45.060 +08:00 [DBG] info: 2025/7/15 11:19:45.060 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-15 11:19:45.065 +08:00 [INF] TopicService initialized and database ensured.
2025-07-15 11:19:45.234 +08:00 [INF] Initializing SemanticKernelService...
2025-07-15 11:19:45.252 +08:00 [INF] SemanticKernelService initialized.
2025-07-15 11:19:48.437 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-15 11:19:52.341 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-15 11:19:52.461 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-15 11:19:52.466 +08:00 [INF] Getting topics for user: llk
2025-07-15 11:19:52.969 +08:00 [DBG] info: 2025/7/15 11:19:52.969 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-15 11:19:53.030 +08:00 [INF] Getting messages for topic ID: 35
2025-07-15 11:19:53.045 +08:00 [DBG] info: 2025/7/15 11:19:53.045 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='35'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-15 11:19:53.075 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-15 11:19:53.076 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-15 11:21:44.067 +08:00 [INF] Application Shutting Down
2025-07-15 11:21:44.071 +08:00 [DBG] Hosting stopping
2025-07-15 11:21:44.074 +08:00 [INF] Application is shutting down...
2025-07-15 11:21:44.081 +08:00 [DBG] Hosting stopped
2025-07-15 11:21:54.538 +08:00 [DBG] Hosting starting
2025-07-15 11:21:54.604 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 11:21:54.611 +08:00 [INF] Hosting environment: Production
2025-07-15 11:21:54.613 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-15 11:21:54.614 +08:00 [DBG] Hosting started
2025-07-15 11:21:54.616 +08:00 [INF] Application Starting Up
2025-07-15 11:21:55.601 +08:00 [DBG] warn: 2025/7/15 11:21:55.601 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-15 11:21:55.757 +08:00 [DBG] info: 2025/7/15 11:21:55.757 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-15 11:21:55.762 +08:00 [INF] TopicService initialized and database ensured.
2025-07-15 11:21:55.939 +08:00 [INF] Initializing SemanticKernelService...
2025-07-15 11:21:55.959 +08:00 [INF] SemanticKernelService initialized.
2025-07-15 11:21:59.611 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-15 11:22:03.495 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-15 11:22:03.613 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-15 11:22:03.619 +08:00 [INF] Getting topics for user: llk
2025-07-15 11:22:04.111 +08:00 [DBG] info: 2025/7/15 11:22:04.111 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-15 11:22:04.171 +08:00 [INF] Getting messages for topic ID: 35
2025-07-15 11:22:04.186 +08:00 [DBG] info: 2025/7/15 11:22:04.186 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='35'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-15 11:22:04.214 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-15 11:22:04.215 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-15 11:22:08.463 +08:00 [ERR] aa https://pic.rmb.bdstatic.com/4c0f7604353cce8445ba3e9ccbe514ad.jpeg
2025-07-15 11:23:33.485 +08:00 [INF] Application Shutting Down
2025-07-15 11:23:33.489 +08:00 [DBG] Hosting stopping
2025-07-15 11:23:33.490 +08:00 [INF] Application is shutting down...
2025-07-15 11:23:33.493 +08:00 [DBG] Hosting stopped
2025-07-15 11:23:49.408 +08:00 [DBG] Hosting starting
2025-07-15 11:23:49.475 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 11:23:49.481 +08:00 [INF] Hosting environment: Production
2025-07-15 11:23:49.483 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-15 11:23:49.485 +08:00 [DBG] Hosting started
2025-07-15 11:23:49.487 +08:00 [INF] Application Starting Up
2025-07-15 11:23:50.477 +08:00 [DBG] warn: 2025/7/15 11:23:50.476 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-15 11:23:50.632 +08:00 [DBG] info: 2025/7/15 11:23:50.632 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (10ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-15 11:23:50.637 +08:00 [INF] TopicService initialized and database ensured.
2025-07-15 11:23:50.820 +08:00 [INF] Initializing SemanticKernelService...
2025-07-15 11:23:50.839 +08:00 [INF] SemanticKernelService initialized.
2025-07-15 11:23:54.180 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-15 11:23:58.594 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-15 11:23:58.719 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-15 11:23:58.725 +08:00 [INF] Getting topics for user: llk
2025-07-15 11:23:59.252 +08:00 [DBG] info: 2025/7/15 11:23:59.252 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (6ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-15 11:23:59.314 +08:00 [INF] Getting messages for topic ID: 35
2025-07-15 11:23:59.330 +08:00 [DBG] info: 2025/7/15 11:23:59.330 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='35'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-15 11:23:59.359 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-15 11:23:59.360 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-15 11:24:01.830 +08:00 [ERR] aa1 https://pic.rmb.bdstatic.com/4c0f7604353cce8445ba3e9ccbe514ad.jpeg
2025-07-15 11:24:02.616 +08:00 [ERR] aa1 https://pic.rmb.bdstatic.com/4c0f7604353cce8445ba3e9ccbe514ad.jpeg
2025-07-15 11:24:03.258 +08:00 [ERR] aa1 https://pic.rmb.bdstatic.com/4c0f7604353cce8445ba3e9ccbe514ad.jpeg
2025-07-15 11:24:21.714 +08:00 [ERR] aa1 https://pic.rmb.bdstatic.com/4c0f7604353cce8445ba3e9ccbe514ad.jpeg
2025-07-15 11:26:26.649 +08:00 [ERR] aa1 https://pic.rmb.bdstatic.com/4c0f7604353cce8445ba3e9ccbe514ad.jpeg
2025-07-15 11:30:27.137 +08:00 [ERR] aa1 https://pic.rmb.bdstatic.com/4c0f7604353cce8445ba3e9ccbe514ad.jpeg
2025-07-15 11:32:36.861 +08:00 [INF] Application Shutting Down
2025-07-15 11:32:36.866 +08:00 [DBG] Hosting stopping
2025-07-15 11:32:36.867 +08:00 [INF] Application is shutting down...
2025-07-15 11:32:36.871 +08:00 [DBG] Hosting stopped
2025-07-15 11:33:02.474 +08:00 [DBG] Hosting starting
2025-07-15 11:33:02.537 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 11:33:02.544 +08:00 [INF] Hosting environment: Production
2025-07-15 11:33:02.546 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-15 11:33:02.548 +08:00 [DBG] Hosting started
2025-07-15 11:33:02.549 +08:00 [INF] Application Starting Up
2025-07-15 11:33:03.542 +08:00 [DBG] warn: 2025/7/15 11:33:03.542 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-15 11:33:03.695 +08:00 [DBG] info: 2025/7/15 11:33:03.695 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (10ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-15 11:33:03.701 +08:00 [INF] TopicService initialized and database ensured.
2025-07-15 11:33:03.875 +08:00 [INF] Initializing SemanticKernelService...
2025-07-15 11:33:03.895 +08:00 [INF] SemanticKernelService initialized.
2025-07-15 11:33:07.151 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-15 11:33:11.085 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-15 11:33:11.207 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-15 11:33:11.213 +08:00 [INF] Getting topics for user: llk
2025-07-15 11:33:11.741 +08:00 [DBG] info: 2025/7/15 11:33:11.741 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-15 11:33:11.806 +08:00 [INF] Getting messages for topic ID: 35
2025-07-15 11:33:11.823 +08:00 [DBG] info: 2025/7/15 11:33:11.823 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='35'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-15 11:33:11.854 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-15 11:33:11.855 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-15 11:34:33.201 +08:00 [INF] Application Shutting Down
2025-07-15 11:34:33.206 +08:00 [DBG] Hosting stopping
2025-07-15 11:34:33.208 +08:00 [INF] Application is shutting down...
2025-07-15 11:34:33.213 +08:00 [DBG] Hosting stopped
2025-07-15 11:34:38.721 +08:00 [DBG] Hosting starting
2025-07-15 11:34:38.783 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 11:34:38.789 +08:00 [INF] Hosting environment: Production
2025-07-15 11:34:38.791 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-15 11:34:38.792 +08:00 [DBG] Hosting started
2025-07-15 11:34:38.795 +08:00 [INF] Application Starting Up
2025-07-15 11:34:39.792 +08:00 [DBG] warn: 2025/7/15 11:34:39.791 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-15 11:34:39.949 +08:00 [DBG] info: 2025/7/15 11:34:39.949 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-15 11:34:39.955 +08:00 [INF] TopicService initialized and database ensured.
2025-07-15 11:34:40.127 +08:00 [INF] Initializing SemanticKernelService...
2025-07-15 11:34:40.146 +08:00 [INF] SemanticKernelService initialized.
2025-07-15 11:34:45.397 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-15 11:34:49.218 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-15 11:34:49.337 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-15 11:34:49.342 +08:00 [INF] Getting topics for user: llk
2025-07-15 11:34:49.837 +08:00 [DBG] info: 2025/7/15 11:34:49.837 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-15 11:34:49.897 +08:00 [INF] Getting messages for topic ID: 35
2025-07-15 11:34:49.913 +08:00 [DBG] info: 2025/7/15 11:34:49.913 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='35'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-15 11:34:49.941 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-15 11:34:49.942 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-15 11:34:53.085 +08:00 [ERR] aa 3840 2160
2025-07-15 15:24:05.042 +08:00 [INF] Application Shutting Down
2025-07-15 15:24:05.046 +08:00 [DBG] Hosting stopping
2025-07-15 15:24:05.047 +08:00 [INF] Application is shutting down...
2025-07-15 15:24:05.049 +08:00 [DBG] Hosting stopped
2025-07-15 15:31:01.533 +08:00 [DBG] Hosting starting
2025-07-15 15:31:01.599 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 15:31:01.606 +08:00 [INF] Hosting environment: Production
2025-07-15 15:31:01.608 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-15 15:31:01.610 +08:00 [DBG] Hosting started
2025-07-15 15:31:01.611 +08:00 [INF] Application Starting Up
2025-07-15 15:31:02.576 +08:00 [DBG] warn: 2025/7/15 15:31:02.576 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-15 15:31:02.744 +08:00 [DBG] info: 2025/7/15 15:31:02.744 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-15 15:31:02.749 +08:00 [INF] TopicService initialized and database ensured.
2025-07-15 15:31:02.920 +08:00 [INF] Initializing SemanticKernelService...
2025-07-15 15:31:02.939 +08:00 [INF] SemanticKernelService initialized.
2025-07-15 15:31:07.160 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-15 15:31:10.854 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-15 15:31:10.973 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-15 15:31:10.978 +08:00 [INF] Getting topics for user: llk
2025-07-15 15:31:11.482 +08:00 [DBG] info: 2025/7/15 15:31:11.482 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (6ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-15 15:31:11.546 +08:00 [INF] Getting messages for topic ID: 35
2025-07-15 15:31:11.561 +08:00 [DBG] info: 2025/7/15 15:31:11.561 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='35'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-15 15:31:11.588 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-15 15:31:11.589 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-15 15:41:31.869 +08:00 [INF] Application Shutting Down
2025-07-15 15:41:31.874 +08:00 [DBG] Hosting stopping
2025-07-15 15:41:31.875 +08:00 [INF] Application is shutting down...
2025-07-15 15:41:31.884 +08:00 [DBG] Hosting stopped
2025-07-15 15:43:02.719 +08:00 [DBG] Hosting starting
2025-07-15 15:43:02.784 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 15:43:02.790 +08:00 [INF] Hosting environment: Production
2025-07-15 15:43:02.792 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-15 15:43:02.794 +08:00 [DBG] Hosting started
2025-07-15 15:43:02.796 +08:00 [INF] Application Starting Up
2025-07-15 15:43:03.785 +08:00 [DBG] warn: 2025/7/15 15:43:03.785 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-15 15:43:03.944 +08:00 [DBG] info: 2025/7/15 15:43:03.944 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-15 15:43:03.950 +08:00 [INF] TopicService initialized and database ensured.
2025-07-15 15:43:04.124 +08:00 [INF] Initializing SemanticKernelService...
2025-07-15 15:43:04.143 +08:00 [INF] SemanticKernelService initialized.
2025-07-15 15:43:07.602 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-15 15:43:11.407 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-15 15:43:11.527 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-15 15:43:11.532 +08:00 [INF] Getting topics for user: llk
2025-07-15 15:43:12.047 +08:00 [DBG] info: 2025/7/15 15:43:12.047 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-15 15:43:12.107 +08:00 [INF] Getting messages for topic ID: 35
2025-07-15 15:43:12.123 +08:00 [DBG] info: 2025/7/15 15:43:12.123 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='35'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-15 15:43:12.152 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-15 15:43:12.153 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-15 15:53:09.226 +08:00 [DBG] Hosting starting
2025-07-15 15:53:09.288 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 15:53:09.295 +08:00 [INF] Hosting environment: Production
2025-07-15 15:53:09.297 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-15 15:53:09.299 +08:00 [DBG] Hosting started
2025-07-15 15:53:09.300 +08:00 [INF] Application Starting Up
2025-07-15 15:53:10.295 +08:00 [DBG] warn: 2025/7/15 15:53:10.295 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-15 15:53:10.456 +08:00 [DBG] info: 2025/7/15 15:53:10.456 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-15 15:53:10.461 +08:00 [INF] TopicService initialized and database ensured.
2025-07-15 15:53:10.629 +08:00 [INF] Initializing SemanticKernelService...
2025-07-15 15:53:10.648 +08:00 [INF] SemanticKernelService initialized.
2025-07-15 15:53:15.905 +08:00 [INF] MCP filesystem 插件已添加: filesystem
2025-07-15 15:53:22.078 +08:00 [INF] MCP database-server 插件已添加: database_server
2025-07-15 15:53:22.202 +08:00 [INF] MCP playwright 插件已添加: playwright
2025-07-15 15:53:22.208 +08:00 [INF] Getting topics for user: llk
2025-07-15 15:53:22.720 +08:00 [DBG] info: 2025/7/15 15:53:22.720 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-15 15:53:22.780 +08:00 [INF] Getting messages for topic ID: 35
2025-07-15 15:53:22.795 +08:00 [DBG] info: 2025/7/15 15:53:22.795 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='35'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-15 15:53:22.823 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-15 15:53:22.824 +08:00 [INF] Setting conversation history. Message count: 2
