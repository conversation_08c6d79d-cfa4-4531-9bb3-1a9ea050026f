2025-07-03 16:18:13.192 +08:00 [DBG] Hosting starting
2025-07-03 16:18:13.264 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-03 16:18:13.270 +08:00 [INF] Hosting environment: Production
2025-07-03 16:18:13.274 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-03 16:18:13.275 +08:00 [DBG] Hosting started
2025-07-03 16:18:13.277 +08:00 [INF] Application Starting Up
2025-07-03 16:18:14.392 +08:00 [DBG] warn: 2025/7/3 16:18:14.392 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-03 16:18:14.581 +08:00 [DBG] info: 2025/7/3 16:18:14.581 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (13ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-03 16:18:14.587 +08:00 [INF] TopicService initialized and database ensured.
2025-07-03 16:18:14.775 +08:00 [INF] Initializing SemanticKernelService...
2025-07-03 16:18:14.794 +08:00 [INF] SemanticKernelService initialized.
2025-07-03 16:18:15.009 +08:00 [DBG] info: 2025/7/3 16:18:15.009 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-03 16:18:15.011 +08:00 [INF] TopicService initialized and database ensured.
2025-07-03 16:18:15.248 +08:00 [INF] Initializing SemanticKernelService...
2025-07-03 16:18:15.250 +08:00 [INF] SemanticKernelService initialized.
2025-07-03 16:18:46.018 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-03 16:18:46.031 +08:00 [INF] Getting topics for user: llk
2025-07-03 16:18:46.551 +08:00 [DBG] info: 2025/7/3 16:18:46.551 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-03 16:18:46.640 +08:00 [INF] Getting messages for topic ID: 7
2025-07-03 16:18:46.655 +08:00 [DBG] info: 2025/7/3 16:18:46.655 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-03 16:18:46.679 +08:00 [INF] Setting conversation history. Message count: 4
2025-07-03 16:18:46.921 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-03 16:18:46.923 +08:00 [INF] Getting topics for user: llk
2025-07-03 16:18:46.928 +08:00 [DBG] info: 2025/7/3 16:18:46.928 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-03 16:18:46.932 +08:00 [INF] Getting messages for topic ID: 7
2025-07-03 16:18:46.934 +08:00 [DBG] info: 2025/7/3 16:18:46.934 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-03 16:18:46.936 +08:00 [INF] Setting conversation history. Message count: 4
2025-07-03 16:19:27.218 +08:00 [INF] Creating topic '新话题 16:19:27' for user: llk
2025-07-03 16:19:27.325 +08:00 [DBG] info: 2025/7/3 16:19:27.325 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='2025-07-03T16:19:27.2210413+08:00' (DbType = DateTime), @p1='新话题 16:19:27' (Nullable = false) (Size = 12), @p2='llk' (Nullable = false) (Size = 3)], CommandType='Text', CommandTimeout='30']
      INSERT INTO "Topics" ("CreatedAt", "Name", "UserName")
      VALUES (@p0, @p1, @p2)
      RETURNING "Id";
2025-07-03 16:19:27.480 +08:00 [INF] Topic '新话题 16:19:27' created with ID: 8
2025-07-03 16:19:27.486 +08:00 [INF] Getting messages for topic ID: 8
2025-07-03 16:19:27.488 +08:00 [DBG] info: 2025/7/3 16:19:27.488 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='8'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-03 16:19:27.492 +08:00 [INF] Setting conversation history. Message count: 0
2025-07-03 16:19:37.976 +08:00 [INF] Adding message to topic ID: 8
2025-07-03 16:19:37.986 +08:00 [DBG] info: 2025/7/3 16:19:37.986 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='使用的模型是什么?' (Nullable = false) (Size = 9), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-03T16:19:37.9759539+08:00' (DbType = DateTime), @p4='8'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-03 16:19:37.995 +08:00 [INF] Getting chat response for user message: 使用的模型是什么?
2025-07-03 16:19:42.738 +08:00 [INF] Received chat response: 我是一个由 Google 训练的语言模型。
2025-07-03 16:19:42.740 +08:00 [INF] Adding message to topic ID: 8
2025-07-03 16:19:42.743 +08:00 [DBG] info: 2025/7/3 16:19:42.743 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='我是一个由 Google 训练的语言模型。' (Nullable = false) (Size = 21), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-03T16:19:42.7399684+08:00' (DbType = DateTime), @p4='8'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-03 16:27:45.278 +08:00 [INF] Application Shutting Down
2025-07-03 16:27:45.282 +08:00 [DBG] Hosting stopping
2025-07-03 16:27:45.283 +08:00 [INF] Application is shutting down...
2025-07-03 16:27:45.286 +08:00 [DBG] Hosting stopped
2025-07-03 16:46:01.583 +08:00 [DBG] Hosting starting
2025-07-03 16:46:01.648 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-03 16:46:01.655 +08:00 [INF] Hosting environment: Production
2025-07-03 16:46:01.658 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-03 16:46:01.659 +08:00 [DBG] Hosting started
2025-07-03 16:46:01.661 +08:00 [INF] Application Starting Up
2025-07-03 16:46:02.614 +08:00 [DBG] warn: 2025/7/3 16:46:02.614 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-03 16:46:02.785 +08:00 [DBG] info: 2025/7/3 16:46:02.785 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (13ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-03 16:46:02.791 +08:00 [INF] TopicService initialized and database ensured.
2025-07-03 16:46:02.956 +08:00 [INF] Initializing SemanticKernelService...
2025-07-03 16:46:02.976 +08:00 [INF] SemanticKernelService initialized.
2025-07-03 16:46:03.180 +08:00 [DBG] info: 2025/7/3 16:46:03.180 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-03 16:46:03.182 +08:00 [INF] TopicService initialized and database ensured.
2025-07-03 16:46:03.383 +08:00 [INF] Initializing SemanticKernelService...
2025-07-03 16:46:03.385 +08:00 [INF] SemanticKernelService initialized.
2025-07-03 16:46:07.053 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-03 16:46:07.058 +08:00 [INF] Getting topics for user: llk
2025-07-03 16:46:07.575 +08:00 [DBG] info: 2025/7/3 16:46:07.575 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-03 16:46:07.637 +08:00 [INF] Getting messages for topic ID: 8
2025-07-03 16:46:07.653 +08:00 [DBG] info: 2025/7/3 16:46:07.653 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='8'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-03 16:46:07.677 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-03 16:46:08.020 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-03 16:46:08.022 +08:00 [INF] Getting topics for user: llk
2025-07-03 16:46:08.027 +08:00 [DBG] info: 2025/7/3 16:46:08.027 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-03 16:46:08.030 +08:00 [INF] Getting messages for topic ID: 8
2025-07-03 16:46:08.033 +08:00 [DBG] info: 2025/7/3 16:46:08.033 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='8'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-03 16:46:08.035 +08:00 [INF] Setting conversation history. Message count: 2
2025-07-03 16:46:25.536 +08:00 [INF] Creating topic '新话题 16:46:25' for user: llk
2025-07-03 16:46:25.645 +08:00 [DBG] info: 2025/7/3 16:46:25.645 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='2025-07-03T16:46:25.5388099+08:00' (DbType = DateTime), @p1='新话题 16:46:25' (Nullable = false) (Size = 12), @p2='llk' (Nullable = false) (Size = 3)], CommandType='Text', CommandTimeout='30']
      INSERT INTO "Topics" ("CreatedAt", "Name", "UserName")
      VALUES (@p0, @p1, @p2)
      RETURNING "Id";
2025-07-03 16:46:25.671 +08:00 [INF] Topic '新话题 16:46:25' created with ID: 9
2025-07-03 16:46:25.676 +08:00 [INF] Getting messages for topic ID: 9
2025-07-03 16:46:25.678 +08:00 [DBG] info: 2025/7/3 16:46:25.678 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='9'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-03 16:46:25.680 +08:00 [INF] Setting conversation history. Message count: 0
2025-07-03 16:46:31.790 +08:00 [INF] Adding message to topic ID: 9
2025-07-03 16:46:31.799 +08:00 [DBG] info: 2025/7/3 16:46:31.799 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='使用的模型是什么?' (Nullable = false) (Size = 9), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-03T16:46:31.7895500+08:00' (DbType = DateTime), @p4='9'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-03 16:46:31.818 +08:00 [INF] Generating topic title for user message: 使用的模型是什么?
2025-07-03 16:46:47.491 +08:00 [INF] Generated topic title: 询问模型
2025-07-03 16:46:47.493 +08:00 [INF] Updating topic ID: 9
2025-07-03 16:46:47.498 +08:00 [DBG] info: 2025/7/3 16:46:47.498 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p3='9', @p0='2025-07-03T16:46:25.5388099+08:00' (DbType = DateTime), @p1='询问模型' (Nullable = false) (Size = 4), @p2='llk' (Nullable = false) (Size = 3)], CommandType='Text', CommandTimeout='30']
      UPDATE "Topics" SET "CreatedAt" = @p0, "Name" = @p1, "UserName" = @p2
      WHERE "Id" = @p3
      RETURNING 1;
2025-07-03 16:46:47.503 +08:00 [INF] Getting chat response for user message: 使用的模型是什么?
2025-07-03 16:46:49.542 +08:00 [INF] Received chat response: 我是一个由 Google 训练的语言模型。
2025-07-03 16:46:49.544 +08:00 [INF] Adding message to topic ID: 9
2025-07-03 16:46:49.547 +08:00 [DBG] info: 2025/7/3 16:46:49.547 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='我是一个由 Google 训练的语言模型。' (Nullable = false) (Size = 21), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-03T16:46:49.5447759+08:00' (DbType = DateTime), @p4='9'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-03 16:47:15.857 +08:00 [INF] Adding message to topic ID: 9
2025-07-03 16:47:15.859 +08:00 [DBG] info: 2025/7/3 16:47:15.859 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='具体型号是什么？' (Nullable = false) (Size = 8), @p1='user' (Nullable = false) (Size = 4), @p2='llk' (Size = 3), @p3='2025-07-03T16:47:15.8571684+08:00' (DbType = DateTime), @p4='9'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-03 16:47:15.864 +08:00 [INF] Getting chat response for user message: 具体型号是什么？
2025-07-03 16:47:17.922 +08:00 [INF] Received chat response: 我是谷歌训练的一个大型语言模型。
2025-07-03 16:47:17.923 +08:00 [INF] Adding message to topic ID: 9
2025-07-03 16:47:17.926 +08:00 [DBG] info: 2025/7/3 16:47:17.926 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='我是谷歌训练的一个大型语言模型。' (Nullable = false) (Size = 16), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-03T16:47:17.9237578+08:00' (DbType = DateTime), @p4='9'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-03 16:47:37.157 +08:00 [INF] Application Shutting Down
2025-07-03 16:47:37.161 +08:00 [DBG] Hosting stopping
2025-07-03 16:47:37.162 +08:00 [INF] Application is shutting down...
2025-07-03 16:47:37.165 +08:00 [DBG] Hosting stopped
2025-07-03 16:47:44.652 +08:00 [DBG] Hosting starting
2025-07-03 16:47:44.713 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-03 16:47:44.720 +08:00 [INF] Hosting environment: Production
2025-07-03 16:47:44.722 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-03 16:47:44.724 +08:00 [DBG] Hosting started
2025-07-03 16:47:44.725 +08:00 [INF] Application Starting Up
2025-07-03 16:47:45.653 +08:00 [DBG] warn: 2025/7/3 16:47:45.653 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-03 16:47:45.813 +08:00 [DBG] info: 2025/7/3 16:47:45.813 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-03 16:47:45.819 +08:00 [INF] TopicService initialized and database ensured.
2025-07-03 16:47:45.979 +08:00 [INF] Initializing SemanticKernelService...
2025-07-03 16:47:45.997 +08:00 [INF] SemanticKernelService initialized.
2025-07-03 16:47:46.200 +08:00 [DBG] info: 2025/7/3 16:47:46.200 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-03 16:47:46.202 +08:00 [INF] TopicService initialized and database ensured.
2025-07-03 16:47:46.410 +08:00 [INF] Initializing SemanticKernelService...
2025-07-03 16:47:46.412 +08:00 [INF] SemanticKernelService initialized.
2025-07-03 16:47:49.241 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-03 16:47:49.247 +08:00 [INF] Getting topics for user: llk
2025-07-03 16:47:49.747 +08:00 [DBG] info: 2025/7/3 16:47:49.747 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-03 16:47:49.807 +08:00 [INF] Getting messages for topic ID: 9
2025-07-03 16:47:49.822 +08:00 [DBG] info: 2025/7/3 16:47:49.822 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='9'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-03 16:47:49.845 +08:00 [INF] Setting conversation history. Message count: 4
2025-07-03 16:47:49.924 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-03 16:47:49.925 +08:00 [INF] Getting topics for user: llk
2025-07-03 16:47:49.930 +08:00 [DBG] info: 2025/7/3 16:47:49.930 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-03 16:47:49.933 +08:00 [INF] Getting messages for topic ID: 9
2025-07-03 16:47:49.936 +08:00 [DBG] info: 2025/7/3 16:47:49.936 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='9'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-03 16:47:49.938 +08:00 [INF] Setting conversation history. Message count: 4
2025-07-03 16:48:06.829 +08:00 [INF] Application Shutting Down
2025-07-03 16:48:06.833 +08:00 [DBG] Hosting stopping
2025-07-03 16:48:06.835 +08:00 [INF] Application is shutting down...
2025-07-03 16:48:06.836 +08:00 [DBG] Hosting stopped
2025-07-03 16:49:22.737 +08:00 [DBG] Hosting starting
2025-07-03 16:49:22.797 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-03 16:49:22.803 +08:00 [INF] Hosting environment: Production
2025-07-03 16:49:22.805 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-03 16:49:22.807 +08:00 [DBG] Hosting started
2025-07-03 16:49:22.808 +08:00 [INF] Application Starting Up
2025-07-03 16:49:23.754 +08:00 [DBG] warn: 2025/7/3 16:49:23.754 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-03 16:49:23.919 +08:00 [DBG] info: 2025/7/3 16:49:23.919 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-03 16:49:23.925 +08:00 [INF] TopicService initialized and database ensured.
2025-07-03 16:49:24.085 +08:00 [INF] Initializing SemanticKernelService...
2025-07-03 16:49:24.104 +08:00 [INF] SemanticKernelService initialized.
2025-07-03 16:49:24.308 +08:00 [DBG] info: 2025/7/3 16:49:24.308 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-03 16:49:24.310 +08:00 [INF] TopicService initialized and database ensured.
2025-07-03 16:49:24.520 +08:00 [INF] Initializing SemanticKernelService...
2025-07-03 16:49:24.522 +08:00 [INF] SemanticKernelService initialized.
2025-07-03 16:49:27.024 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-03 16:49:27.029 +08:00 [INF] Getting topics for user: llk
2025-07-03 16:49:27.543 +08:00 [DBG] info: 2025/7/3 16:49:27.543 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-03 16:49:27.607 +08:00 [INF] Getting messages for topic ID: 9
2025-07-03 16:49:27.623 +08:00 [DBG] info: 2025/7/3 16:49:27.623 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='9'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-03 16:49:27.647 +08:00 [INF] Setting conversation history. Message count: 4
2025-07-03 16:49:27.727 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-03 16:49:27.728 +08:00 [INF] Getting topics for user: llk
2025-07-03 16:49:27.733 +08:00 [DBG] info: 2025/7/3 16:49:27.733 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-03 16:49:27.735 +08:00 [INF] Getting messages for topic ID: 9
2025-07-03 16:49:27.739 +08:00 [DBG] info: 2025/7/3 16:49:27.739 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='9'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-03 16:49:27.741 +08:00 [INF] Setting conversation history. Message count: 4
2025-07-03 16:49:44.763 +08:00 [INF] Application Shutting Down
2025-07-03 16:49:44.766 +08:00 [DBG] Hosting stopping
2025-07-03 16:49:44.768 +08:00 [INF] Application is shutting down...
2025-07-03 16:49:44.769 +08:00 [DBG] Hosting stopped
2025-07-03 16:52:16.401 +08:00 [DBG] Hosting starting
2025-07-03 16:52:16.464 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-03 16:52:16.470 +08:00 [INF] Hosting environment: Production
2025-07-03 16:52:16.472 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-03 16:52:16.473 +08:00 [DBG] Hosting started
2025-07-03 16:52:16.474 +08:00 [INF] Application Starting Up
2025-07-03 16:52:17.407 +08:00 [DBG] warn: 2025/7/3 16:52:17.407 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-03 16:52:17.572 +08:00 [DBG] info: 2025/7/3 16:52:17.572 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-03 16:52:17.577 +08:00 [INF] TopicService initialized and database ensured.
2025-07-03 16:52:17.742 +08:00 [INF] Initializing SemanticKernelService...
2025-07-03 16:52:17.761 +08:00 [INF] SemanticKernelService initialized.
2025-07-03 16:52:22.019 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-03 16:52:22.025 +08:00 [INF] Getting topics for user: llk
2025-07-03 16:52:22.535 +08:00 [DBG] info: 2025/7/3 16:52:22.535 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-03 16:52:22.597 +08:00 [INF] Getting messages for topic ID: 9
2025-07-03 16:52:22.611 +08:00 [DBG] info: 2025/7/3 16:52:22.611 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='9'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-03 16:52:22.634 +08:00 [INF] Setting conversation history. Message count: 4
2025-07-03 16:57:14.019 +08:00 [INF] Application Shutting Down
2025-07-03 16:57:14.022 +08:00 [DBG] Hosting stopping
2025-07-03 16:57:14.024 +08:00 [INF] Application is shutting down...
2025-07-03 16:57:14.025 +08:00 [DBG] Hosting stopped
2025-07-03 17:25:10.199 +08:00 [DBG] Hosting starting
2025-07-03 17:25:10.261 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-03 17:25:10.267 +08:00 [INF] Hosting environment: Production
2025-07-03 17:25:10.269 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai\bin\Debug\net9.0-windows10.0.26100.0
2025-07-03 17:25:10.270 +08:00 [DBG] Hosting started
2025-07-03 17:25:10.272 +08:00 [INF] Application Starting Up
2025-07-03 17:25:11.236 +08:00 [DBG] warn: 2025/7/3 17:25:11.236 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-03 17:25:11.403 +08:00 [DBG] info: 2025/7/3 17:25:11.403 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-03 17:25:11.409 +08:00 [INF] TopicService initialized and database ensured.
2025-07-03 17:25:11.570 +08:00 [INF] Initializing SemanticKernelService...
2025-07-03 17:25:11.589 +08:00 [INF] SemanticKernelService initialized.
2025-07-03 17:25:15.063 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-03 17:25:15.068 +08:00 [INF] Getting topics for user: llk
2025-07-03 17:25:15.582 +08:00 [DBG] info: 2025/7/3 17:25:15.582 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-03 17:25:15.644 +08:00 [INF] Getting messages for topic ID: 9
2025-07-03 17:25:15.660 +08:00 [DBG] info: 2025/7/3 17:25:15.660 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='9'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-03 17:25:15.683 +08:00 [INF] Setting conversation history. Message count: 4
