﻿#pragma checksum "..\..\..\ImageViewerWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1F60A8FD4E1F2DA829813F6B974D32C0DDADECF9"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace Yidev.LocalAI {
    
    
    /// <summary>
    /// ImageViewerWindow
    /// </summary>
    public partial class ImageViewerWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 117 "..\..\..\ImageViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\ImageViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer ImageScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\ImageViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image MainImage;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\ImageViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel LoadingIndicator;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\ImageViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.RotateTransform LoadingRotation;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\ImageViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ErrorIndicator;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\ImageViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ErrorMessage;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\ImageViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FitToWindowButton;
        
        #line default
        #line hidden
        
        
        #line 226 "..\..\..\ImageViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ActualSizeButton;
        
        #line default
        #line hidden
        
        
        #line 237 "..\..\..\ImageViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenInBrowserButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/Yidev.LocalAI;component/imageviewerwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\ImageViewerWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 121 "..\..\..\ImageViewerWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.ImageScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 3:
            this.MainImage = ((System.Windows.Controls.Image)(target));
            return;
            case 4:
            this.LoadingIndicator = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 5:
            this.LoadingRotation = ((System.Windows.Media.RotateTransform)(target));
            return;
            case 6:
            this.ErrorIndicator = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 7:
            this.ErrorMessage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.FitToWindowButton = ((System.Windows.Controls.Button)(target));
            
            #line 217 "..\..\..\ImageViewerWindow.xaml"
            this.FitToWindowButton.Click += new System.Windows.RoutedEventHandler(this.FitToWindow_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.ActualSizeButton = ((System.Windows.Controls.Button)(target));
            
            #line 228 "..\..\..\ImageViewerWindow.xaml"
            this.ActualSizeButton.Click += new System.Windows.RoutedEventHandler(this.ActualSize_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.OpenInBrowserButton = ((System.Windows.Controls.Button)(target));
            
            #line 239 "..\..\..\ImageViewerWindow.xaml"
            this.OpenInBrowserButton.Click += new System.Windows.RoutedEventHandler(this.OpenInBrowser_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

