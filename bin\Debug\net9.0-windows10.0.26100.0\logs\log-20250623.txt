2025-06-23 11:32:12.809 +08:00 [INF] Application Starting Up
2025-06-23 11:32:13.647 +08:00 [DBG] warn: 2025/6/23 11:32:13.646 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 11:32:13.812 +08:00 [DBG] info: 2025/6/23 11:32:13.812 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (10ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 11:32:13.817 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 11:32:13.820 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 11:32:13.838 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 11:32:13.841 +08:00 [INF] Getting topics for user: llk
2025-06-23 11:32:14.362 +08:00 [DBG] info: 2025/6/23 11:32:14.362 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 11:32:14.423 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 11:32:14.440 +08:00 [DBG] info: 2025/6/23 11:32:14.440 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 11:32:14.463 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 11:35:22.502 +08:00 [INF] Application Shutting Down
2025-06-23 11:35:34.843 +08:00 [INF] Application Starting Up
2025-06-23 11:35:35.674 +08:00 [DBG] warn: 2025/6/23 11:35:35.674 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 11:35:35.846 +08:00 [DBG] info: 2025/6/23 11:35:35.846 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 11:35:35.850 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 11:35:35.855 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 14:28:03.829 +08:00 [INF] Application Starting Up
2025-06-23 14:28:04.673 +08:00 [DBG] warn: 2025/6/23 14:28:04.673 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 14:28:04.845 +08:00 [DBG] info: 2025/6/23 14:28:04.845 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 14:28:04.851 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 14:28:04.857 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 14:31:10.335 +08:00 [INF] Application Starting Up
2025-06-23 14:31:11.201 +08:00 [DBG] warn: 2025/6/23 14:31:11.200 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 14:31:11.376 +08:00 [DBG] info: 2025/6/23 14:31:11.376 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 14:31:11.382 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 14:31:11.387 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 14:33:09.154 +08:00 [INF] Application Starting Up
2025-06-23 14:33:09.978 +08:00 [DBG] warn: 2025/6/23 14:33:09.978 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 14:33:10.145 +08:00 [DBG] info: 2025/6/23 14:33:10.145 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 14:33:10.150 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 14:33:10.155 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 14:33:10.159 +08:00 [ERR] 在 Kernel 工厂方法中添加共享 MCP 插件时出错
2025-06-23 14:34:38.151 +08:00 [INF] Application Starting Up
2025-06-23 14:34:38.990 +08:00 [DBG] warn: 2025/6/23 14:34:38.989 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 14:34:39.157 +08:00 [DBG] info: 2025/6/23 14:34:39.157 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 14:34:39.162 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 14:34:39.167 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 14:45:23.099 +08:00 [INF] Application Starting Up
2025-06-23 14:45:24.135 +08:00 [DBG] warn: 2025/6/23 14:45:24.134 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 14:45:24.300 +08:00 [DBG] info: 2025/6/23 14:45:24.300 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 14:45:24.305 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 14:45:24.473 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 14:45:24.493 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 14:45:27.173 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 14:45:27.178 +08:00 [INF] Getting topics for user: llk
2025-06-23 14:45:27.685 +08:00 [DBG] info: 2025/6/23 14:45:27.685 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 14:45:27.747 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 14:45:27.762 +08:00 [DBG] info: 2025/6/23 14:45:27.762 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 14:45:27.785 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 14:46:04.798 +08:00 [INF] Getting messages for topic ID: 6
2025-06-23 14:46:04.805 +08:00 [DBG] info: 2025/6/23 14:46:04.805 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='6'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 14:46:04.809 +08:00 [INF] Setting conversation history. Message count: 6
2025-06-23 14:46:07.028 +08:00 [INF] Getting messages for topic ID: 1
2025-06-23 14:46:07.030 +08:00 [DBG] info: 2025/6/23 14:46:07.030 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='1'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 14:46:07.033 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 14:46:14.051 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 14:46:14.054 +08:00 [DBG] info: 2025/6/23 14:46:14.054 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 14:46:14.056 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 14:46:15.347 +08:00 [INF] Getting messages for topic ID: 6
2025-06-23 14:46:15.350 +08:00 [DBG] info: 2025/6/23 14:46:15.350 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='6'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 14:46:15.352 +08:00 [INF] Setting conversation history. Message count: 6
2025-06-23 14:46:20.868 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 14:46:20.871 +08:00 [DBG] info: 2025/6/23 14:46:20.871 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 14:46:20.873 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 14:49:55.453 +08:00 [INF] Application Shutting Down
2025-06-23 14:50:01.276 +08:00 [INF] Application Starting Up
2025-06-23 14:50:02.311 +08:00 [DBG] warn: 2025/6/23 14:50:02.311 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 14:50:02.476 +08:00 [DBG] info: 2025/6/23 14:50:02.476 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (10ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 14:50:02.481 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 14:50:02.650 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 14:50:02.669 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 14:50:05.407 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 14:50:05.413 +08:00 [INF] Getting topics for user: llk
2025-06-23 14:50:05.947 +08:00 [DBG] info: 2025/6/23 14:50:05.947 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 14:50:06.007 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 14:50:06.023 +08:00 [DBG] info: 2025/6/23 14:50:06.023 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 14:50:06.044 +08:00 [ERR] aa https://auth.yidev.cn/api/avatar?email=<EMAIL>
2025-06-23 14:50:06.046 +08:00 [ERR] aa pack://application:,,,/uni-ai.png
2025-06-23 14:50:06.048 +08:00 [ERR] aa https://auth.yidev.cn/api/avatar?email=<EMAIL>
2025-06-23 14:50:06.049 +08:00 [ERR] aa pack://application:,,,/uni-ai.png
2025-06-23 14:50:06.051 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 16:10:35.465 +08:00 [INF] Application Shutting Down
2025-06-23 16:18:30.850 +08:00 [INF] Application Starting Up
2025-06-23 16:18:31.944 +08:00 [DBG] warn: 2025/6/23 16:18:31.944 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 16:18:32.114 +08:00 [DBG] info: 2025/6/23 16:18:32.114 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 16:18:32.120 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 16:18:32.301 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 16:18:32.321 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 16:18:35.598 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 16:18:35.604 +08:00 [INF] Getting topics for user: llk
2025-06-23 16:18:36.108 +08:00 [DBG] info: 2025/6/23 16:18:36.108 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 16:18:36.168 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 16:18:36.184 +08:00 [DBG] info: 2025/6/23 16:18:36.184 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 16:18:36.206 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 16:19:12.938 +08:00 [INF] Application Shutting Down
2025-06-23 16:21:29.434 +08:00 [INF] Application Starting Up
2025-06-23 16:21:30.495 +08:00 [DBG] warn: 2025/6/23 16:21:30.494 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 16:21:30.663 +08:00 [DBG] info: 2025/6/23 16:21:30.663 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 16:21:30.669 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 16:21:30.841 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 16:21:30.862 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 16:21:34.432 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 16:21:34.438 +08:00 [INF] Getting topics for user: llk
2025-06-23 16:21:34.942 +08:00 [DBG] info: 2025/6/23 16:21:34.942 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 16:21:35.002 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 16:21:35.017 +08:00 [DBG] info: 2025/6/23 16:21:35.017 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 16:21:35.040 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 16:22:06.339 +08:00 [INF] Application Shutting Down
2025-06-23 16:25:12.738 +08:00 [INF] Application Starting Up
2025-06-23 16:25:13.815 +08:00 [DBG] warn: 2025/6/23 16:25:13.815 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 16:25:13.982 +08:00 [DBG] info: 2025/6/23 16:25:13.982 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (10ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 16:25:13.987 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 16:25:14.151 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 16:25:14.170 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 16:25:17.240 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 16:25:17.246 +08:00 [INF] Getting topics for user: llk
2025-06-23 16:25:17.757 +08:00 [DBG] info: 2025/6/23 16:25:17.757 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 16:25:17.819 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 16:25:17.834 +08:00 [DBG] info: 2025/6/23 16:25:17.834 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 16:25:17.856 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 16:27:27.610 +08:00 [INF] Application Shutting Down
2025-06-23 16:30:32.858 +08:00 [INF] Application Starting Up
2025-06-23 16:30:33.962 +08:00 [DBG] warn: 2025/6/23 16:30:33.962 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 16:30:34.142 +08:00 [DBG] info: 2025/6/23 16:30:34.142 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 16:30:34.148 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 16:30:34.326 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 16:30:34.346 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 16:30:37.789 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 16:30:37.794 +08:00 [INF] Getting topics for user: llk
2025-06-23 16:30:38.326 +08:00 [DBG] info: 2025/6/23 16:30:38.325 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 16:30:38.388 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 16:30:38.404 +08:00 [DBG] info: 2025/6/23 16:30:38.404 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 16:30:38.427 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 16:38:04.126 +08:00 [INF] Application Shutting Down
2025-06-23 16:38:10.268 +08:00 [INF] Application Starting Up
2025-06-23 16:38:11.350 +08:00 [DBG] warn: 2025/6/23 16:38:11.349 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 16:38:11.521 +08:00 [DBG] info: 2025/6/23 16:38:11.521 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 16:38:11.528 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 16:38:11.699 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 16:38:11.719 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 16:38:14.968 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 16:38:14.974 +08:00 [INF] Getting topics for user: llk
2025-06-23 16:38:15.480 +08:00 [DBG] info: 2025/6/23 16:38:15.480 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 16:38:15.541 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 16:38:15.556 +08:00 [DBG] info: 2025/6/23 16:38:15.556 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 16:38:15.579 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 16:42:06.804 +08:00 [INF] Application Shutting Down
2025-06-23 16:42:12.104 +08:00 [INF] Application Starting Up
2025-06-23 16:42:13.176 +08:00 [DBG] warn: 2025/6/23 16:42:13.176 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 16:42:13.351 +08:00 [DBG] info: 2025/6/23 16:42:13.351 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 16:42:13.357 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 16:42:13.530 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 16:42:13.550 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 16:42:16.553 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 16:42:16.558 +08:00 [INF] Getting topics for user: llk
2025-06-23 16:42:17.056 +08:00 [DBG] info: 2025/6/23 16:42:17.056 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 16:42:17.116 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 16:42:17.132 +08:00 [DBG] info: 2025/6/23 16:42:17.132 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 16:42:17.154 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 16:43:31.845 +08:00 [INF] Application Shutting Down
2025-06-23 16:43:53.325 +08:00 [INF] Application Starting Up
2025-06-23 16:43:54.396 +08:00 [DBG] warn: 2025/6/23 16:43:54.396 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 16:43:54.568 +08:00 [DBG] info: 2025/6/23 16:43:54.568 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 16:43:54.574 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 16:43:54.745 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 16:43:54.765 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 16:43:57.644 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 16:43:57.649 +08:00 [INF] Getting topics for user: llk
2025-06-23 16:43:58.161 +08:00 [DBG] info: 2025/6/23 16:43:58.161 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 16:43:58.222 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 16:43:58.237 +08:00 [DBG] info: 2025/6/23 16:43:58.237 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 16:43:58.259 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 16:44:31.858 +08:00 [INF] Application Shutting Down
2025-06-23 16:46:11.123 +08:00 [INF] Application Starting Up
2025-06-23 16:46:12.162 +08:00 [DBG] warn: 2025/6/23 16:46:12.162 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 16:46:12.326 +08:00 [DBG] info: 2025/6/23 16:46:12.326 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 16:46:12.331 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 16:46:12.499 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 16:46:12.518 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 16:46:15.187 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 16:46:15.192 +08:00 [INF] Getting topics for user: llk
2025-06-23 16:46:15.707 +08:00 [DBG] info: 2025/6/23 16:46:15.707 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 16:46:15.766 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 16:46:15.781 +08:00 [DBG] info: 2025/6/23 16:46:15.781 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 16:46:15.803 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 16:47:09.778 +08:00 [INF] Application Shutting Down
2025-06-23 16:48:22.261 +08:00 [INF] Application Starting Up
2025-06-23 16:48:23.346 +08:00 [DBG] warn: 2025/6/23 16:48:23.346 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 16:48:23.522 +08:00 [DBG] info: 2025/6/23 16:48:23.522 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 16:48:23.529 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 16:48:23.711 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 16:48:23.730 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 16:48:27.465 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 16:48:27.470 +08:00 [INF] Getting topics for user: llk
2025-06-23 16:48:27.977 +08:00 [DBG] info: 2025/6/23 16:48:27.977 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 16:48:28.037 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 16:48:28.051 +08:00 [DBG] info: 2025/6/23 16:48:28.051 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 16:48:28.073 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 16:49:00.653 +08:00 [INF] Application Shutting Down
2025-06-23 16:53:18.171 +08:00 [INF] Application Starting Up
2025-06-23 16:53:19.253 +08:00 [DBG] warn: 2025/6/23 16:53:19.252 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 16:53:19.424 +08:00 [DBG] info: 2025/6/23 16:53:19.424 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 16:53:19.431 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 16:53:19.604 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 16:53:19.626 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 16:53:22.406 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 16:53:22.411 +08:00 [INF] Getting topics for user: llk
2025-06-23 16:53:22.916 +08:00 [DBG] info: 2025/6/23 16:53:22.916 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 16:53:22.976 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 16:53:22.992 +08:00 [DBG] info: 2025/6/23 16:53:22.992 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 16:53:23.014 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 16:53:46.758 +08:00 [INF] Application Shutting Down
2025-06-23 16:57:21.036 +08:00 [INF] Application Starting Up
2025-06-23 16:57:22.141 +08:00 [DBG] warn: 2025/6/23 16:57:22.141 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 16:57:22.311 +08:00 [DBG] info: 2025/6/23 16:57:22.311 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 16:57:22.318 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 16:57:22.490 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 16:57:22.510 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 16:57:25.268 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 16:57:25.273 +08:00 [INF] Getting topics for user: llk
2025-06-23 16:57:25.774 +08:00 [DBG] info: 2025/6/23 16:57:25.774 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 16:57:25.836 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 16:57:25.851 +08:00 [DBG] info: 2025/6/23 16:57:25.851 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 16:57:25.876 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 16:58:27.778 +08:00 [INF] Application Shutting Down
2025-06-23 16:58:57.823 +08:00 [INF] Application Starting Up
2025-06-23 16:58:58.871 +08:00 [DBG] warn: 2025/6/23 16:58:58.870 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 16:58:59.046 +08:00 [DBG] info: 2025/6/23 16:58:59.046 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 16:58:59.053 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 16:58:59.230 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 16:58:59.249 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 16:59:02.313 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 16:59:02.318 +08:00 [INF] Getting topics for user: llk
2025-06-23 16:59:02.822 +08:00 [DBG] info: 2025/6/23 16:59:02.822 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 16:59:02.884 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 16:59:02.900 +08:00 [DBG] info: 2025/6/23 16:59:02.900 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 16:59:02.924 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 16:59:19.572 +08:00 [INF] Application Shutting Down
2025-06-23 17:01:10.099 +08:00 [INF] Application Starting Up
2025-06-23 17:01:11.138 +08:00 [DBG] warn: 2025/6/23 17:01:11.138 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 17:01:11.303 +08:00 [DBG] info: 2025/6/23 17:01:11.303 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 17:01:11.308 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 17:01:11.480 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 17:01:11.499 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 17:01:14.181 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 17:01:14.186 +08:00 [INF] Getting topics for user: llk
2025-06-23 17:01:14.696 +08:00 [DBG] info: 2025/6/23 17:01:14.696 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (6ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 17:01:14.761 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 17:01:14.777 +08:00 [DBG] info: 2025/6/23 17:01:14.777 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 17:01:14.799 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 17:02:29.130 +08:00 [INF] Application Shutting Down
2025-06-23 17:04:31.394 +08:00 [INF] Application Starting Up
2025-06-23 17:04:32.443 +08:00 [DBG] warn: 2025/6/23 17:04:32.443 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 17:04:32.616 +08:00 [DBG] info: 2025/6/23 17:04:32.616 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 17:04:32.622 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 17:04:32.798 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 17:04:32.818 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 17:04:35.808 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 17:04:35.814 +08:00 [INF] Getting topics for user: llk
2025-06-23 17:04:36.320 +08:00 [DBG] info: 2025/6/23 17:04:36.320 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 17:04:36.380 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 17:04:36.396 +08:00 [DBG] info: 2025/6/23 17:04:36.396 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 17:04:36.418 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 17:04:59.923 +08:00 [INF] Getting messages for topic ID: 6
2025-06-23 17:04:59.928 +08:00 [DBG] info: 2025/6/23 17:04:59.928 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='6'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 17:04:59.931 +08:00 [INF] Setting conversation history. Message count: 6
2025-06-23 17:06:44.873 +08:00 [INF] Updating topic ID: 6
2025-06-23 17:06:44.959 +08:00 [DBG] info: 2025/6/23 17:06:44.959 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p3='6', @p0='2025-06-23T09:23:41.4201711' (DbType = DateTime), @p1='新话题 09:23:41啊啊啊啊啊啊' (Nullable = false) (Size = 18), @p2='llk' (Nullable = false) (Size = 3)], CommandType='Text', CommandTimeout='30']
      UPDATE "Topics" SET "CreatedAt" = @p0, "Name" = @p1, "UserName" = @p2
      WHERE "Id" = @p3
      RETURNING 1;
2025-06-23 17:06:52.882 +08:00 [INF] Getting messages for topic ID: 1
2025-06-23 17:06:52.885 +08:00 [DBG] info: 2025/6/23 17:06:52.885 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='1'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 17:06:52.887 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 17:07:02.998 +08:00 [INF] Updating topic ID: 1
2025-06-23 17:07:03.001 +08:00 [DBG] info: 2025/6/23 17:07:03.001 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p3='1', @p0='2025-06-21T13:37:21.6516133' (DbType = DateTime), @p1='新话题' (Nullable = false) (Size = 3), @p2='llk' (Nullable = false) (Size = 3)], CommandType='Text', CommandTimeout='30']
      UPDATE "Topics" SET "CreatedAt" = @p0, "Name" = @p1, "UserName" = @p2
      WHERE "Id" = @p3
      RETURNING 1;
2025-06-23 17:12:22.578 +08:00 [INF] Application Shutting Down
2025-06-23 17:12:23.529 +08:00 [INF] Application Starting Up
2025-06-23 17:12:24.609 +08:00 [DBG] warn: 2025/6/23 17:12:24.609 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 17:12:24.782 +08:00 [DBG] info: 2025/6/23 17:12:24.782 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 17:12:24.788 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 17:12:24.959 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 17:12:24.978 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 17:12:27.705 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 17:12:27.711 +08:00 [INF] Getting topics for user: llk
2025-06-23 17:12:28.213 +08:00 [DBG] info: 2025/6/23 17:12:28.213 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 17:12:28.275 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 17:12:28.289 +08:00 [DBG] info: 2025/6/23 17:12:28.289 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 17:12:28.312 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 17:12:57.602 +08:00 [INF] Application Shutting Down
2025-06-23 17:13:00.190 +08:00 [INF] Application Starting Up
2025-06-23 17:13:01.212 +08:00 [DBG] warn: 2025/6/23 17:13:01.211 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 17:13:01.374 +08:00 [DBG] info: 2025/6/23 17:13:01.374 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 17:13:01.380 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 17:13:01.550 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 17:13:01.569 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 17:13:04.354 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 17:13:04.359 +08:00 [INF] Getting topics for user: llk
2025-06-23 17:13:04.865 +08:00 [DBG] info: 2025/6/23 17:13:04.865 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 17:13:04.924 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 17:13:04.939 +08:00 [DBG] info: 2025/6/23 17:13:04.939 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 17:13:04.961 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 17:13:13.599 +08:00 [INF] Application Shutting Down
2025-06-23 17:17:33.624 +08:00 [INF] Application Starting Up
2025-06-23 17:17:34.740 +08:00 [DBG] warn: 2025/6/23 17:17:34.740 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 17:17:34.912 +08:00 [DBG] info: 2025/6/23 17:17:34.912 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 17:17:34.918 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 17:17:35.093 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 17:17:35.114 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 17:17:37.959 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 17:17:37.965 +08:00 [INF] Getting topics for user: llk
2025-06-23 17:17:38.467 +08:00 [DBG] info: 2025/6/23 17:17:38.467 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 17:17:38.528 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 17:17:38.544 +08:00 [DBG] info: 2025/6/23 17:17:38.544 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 17:17:38.568 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 17:18:02.125 +08:00 [INF] Application Shutting Down
2025-06-23 17:18:12.470 +08:00 [INF] Application Starting Up
2025-06-23 17:18:13.539 +08:00 [DBG] warn: 2025/6/23 17:18:13.539 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 17:18:13.711 +08:00 [DBG] info: 2025/6/23 17:18:13.711 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 17:18:13.717 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 17:18:13.886 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 17:18:13.906 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 17:18:16.618 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 17:18:16.623 +08:00 [INF] Getting topics for user: llk
2025-06-23 17:18:17.146 +08:00 [DBG] info: 2025/6/23 17:18:17.146 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 17:18:17.208 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 17:18:17.223 +08:00 [DBG] info: 2025/6/23 17:18:17.223 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 17:18:17.245 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 17:18:34.999 +08:00 [INF] Application Shutting Down
2025-06-23 17:18:42.510 +08:00 [INF] Application Starting Up
2025-06-23 17:18:43.610 +08:00 [DBG] warn: 2025/6/23 17:18:43.610 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 17:18:43.783 +08:00 [DBG] info: 2025/6/23 17:18:43.783 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 17:18:43.789 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 17:18:43.970 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 17:18:43.990 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 17:18:46.719 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 17:18:46.724 +08:00 [INF] Getting topics for user: llk
2025-06-23 17:18:47.237 +08:00 [DBG] info: 2025/6/23 17:18:47.237 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (6ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 17:18:47.297 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 17:18:47.313 +08:00 [DBG] info: 2025/6/23 17:18:47.313 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 17:18:47.336 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 17:19:03.654 +08:00 [INF] Application Shutting Down
2025-06-23 17:19:10.643 +08:00 [INF] Application Starting Up
2025-06-23 17:19:11.733 +08:00 [DBG] warn: 2025/6/23 17:19:11.733 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 17:19:11.907 +08:00 [DBG] info: 2025/6/23 17:19:11.907 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 17:19:11.913 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 17:19:12.090 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 17:19:12.110 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 17:19:15.013 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 17:19:15.019 +08:00 [INF] Getting topics for user: llk
2025-06-23 17:19:15.525 +08:00 [DBG] info: 2025/6/23 17:19:15.525 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 17:19:15.586 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 17:19:15.601 +08:00 [DBG] info: 2025/6/23 17:19:15.601 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 17:19:15.624 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 17:19:30.931 +08:00 [INF] Application Shutting Down
2025-06-23 17:19:38.125 +08:00 [INF] Application Starting Up
2025-06-23 17:19:39.217 +08:00 [DBG] warn: 2025/6/23 17:19:39.217 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 17:19:39.389 +08:00 [DBG] info: 2025/6/23 17:19:39.388 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 17:19:39.394 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 17:19:39.574 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 17:19:39.594 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 17:19:42.387 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 17:19:42.393 +08:00 [INF] Getting topics for user: llk
2025-06-23 17:19:42.904 +08:00 [DBG] info: 2025/6/23 17:19:42.904 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 17:19:42.965 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 17:19:42.980 +08:00 [DBG] info: 2025/6/23 17:19:42.980 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 17:19:43.003 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 17:20:15.103 +08:00 [INF] Application Shutting Down
2025-06-23 17:20:24.838 +08:00 [INF] Application Starting Up
2025-06-23 17:20:25.931 +08:00 [DBG] warn: 2025/6/23 17:20:25.931 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 17:20:26.102 +08:00 [DBG] info: 2025/6/23 17:20:26.102 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 17:20:26.107 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 17:20:26.279 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 17:20:26.298 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 17:20:29.035 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 17:20:29.040 +08:00 [INF] Getting topics for user: llk
2025-06-23 17:20:29.536 +08:00 [DBG] info: 2025/6/23 17:20:29.536 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 17:20:29.596 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 17:20:29.611 +08:00 [DBG] info: 2025/6/23 17:20:29.611 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 17:20:29.634 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 17:20:59.299 +08:00 [INF] Application Shutting Down
2025-06-23 17:23:35.685 +08:00 [INF] Application Starting Up
2025-06-23 17:23:36.711 +08:00 [DBG] warn: 2025/6/23 17:23:36.711 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 17:23:36.875 +08:00 [DBG] info: 2025/6/23 17:23:36.875 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 17:23:36.880 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 17:23:37.046 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 17:23:37.066 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 17:23:39.823 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 17:23:39.829 +08:00 [INF] Getting topics for user: llk
2025-06-23 17:23:40.341 +08:00 [DBG] info: 2025/6/23 17:23:40.341 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 17:23:40.401 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 17:23:40.415 +08:00 [DBG] info: 2025/6/23 17:23:40.415 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 17:23:40.438 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 17:26:38.419 +08:00 [INF] Application Shutting Down
2025-06-23 17:26:45.692 +08:00 [INF] Application Starting Up
2025-06-23 17:26:46.776 +08:00 [DBG] warn: 2025/6/23 17:26:46.775 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 17:26:46.949 +08:00 [DBG] info: 2025/6/23 17:26:46.949 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 17:26:46.955 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 17:26:47.128 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 17:26:47.148 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 17:26:49.849 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 17:26:49.855 +08:00 [INF] Getting topics for user: llk
2025-06-23 17:26:50.385 +08:00 [DBG] info: 2025/6/23 17:26:50.385 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 17:26:50.446 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 17:26:50.461 +08:00 [DBG] info: 2025/6/23 17:26:50.461 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 17:26:50.484 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 17:28:24.298 +08:00 [INF] Application Shutting Down
2025-06-23 17:28:28.996 +08:00 [INF] Application Starting Up
2025-06-23 17:28:30.085 +08:00 [DBG] warn: 2025/6/23 17:28:30.085 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 17:28:30.257 +08:00 [DBG] info: 2025/6/23 17:28:30.257 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 17:28:30.263 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 17:28:30.440 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 17:28:30.460 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 17:28:33.114 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 17:28:33.119 +08:00 [INF] Getting topics for user: llk
2025-06-23 17:28:33.639 +08:00 [DBG] info: 2025/6/23 17:28:33.639 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 17:28:33.700 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 17:28:33.716 +08:00 [DBG] info: 2025/6/23 17:28:33.716 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 17:28:33.739 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 17:28:45.824 +08:00 [INF] Application Shutting Down
2025-06-23 17:29:05.174 +08:00 [INF] Application Starting Up
2025-06-23 17:29:06.260 +08:00 [DBG] warn: 2025/6/23 17:29:06.259 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 17:29:06.432 +08:00 [DBG] info: 2025/6/23 17:29:06.432 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 17:29:06.438 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 17:29:06.621 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 17:29:06.642 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 17:29:09.341 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 17:29:09.347 +08:00 [INF] Getting topics for user: llk
2025-06-23 17:29:09.864 +08:00 [DBG] info: 2025/6/23 17:29:09.864 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 17:29:09.926 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 17:29:09.941 +08:00 [DBG] info: 2025/6/23 17:29:09.941 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 17:29:09.963 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 17:29:28.186 +08:00 [INF] Getting messages for topic ID: 6
2025-06-23 17:29:28.191 +08:00 [DBG] info: 2025/6/23 17:29:28.191 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@__topicId_0='6'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 17:29:28.194 +08:00 [INF] Setting conversation history. Message count: 6
2025-06-23 17:29:41.978 +08:00 [INF] Updating topic ID: 6
2025-06-23 17:29:42.062 +08:00 [DBG] info: 2025/6/23 17:29:42.062 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p3='6', @p0='2025-06-23T09:23:41.4201711' (DbType = DateTime), @p1='新话题 09:23:41啊啊啊啊啊啊八八八八' (Nullable = false) (Size = 22), @p2='llk' (Nullable = false) (Size = 3)], CommandType='Text', CommandTimeout='30']
      UPDATE "Topics" SET "CreatedAt" = @p0, "Name" = @p1, "UserName" = @p2
      WHERE "Id" = @p3
      RETURNING 1;
2025-06-23 17:29:50.194 +08:00 [INF] Application Shutting Down
2025-06-23 17:31:55.904 +08:00 [INF] Application Starting Up
2025-06-23 17:31:56.955 +08:00 [DBG] warn: 2025/6/23 17:31:56.955 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 17:31:57.127 +08:00 [DBG] info: 2025/6/23 17:31:57.127 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 17:31:57.133 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 17:31:57.306 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 17:31:57.326 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 17:31:59.997 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 17:32:00.003 +08:00 [INF] Getting topics for user: llk
2025-06-23 17:32:00.524 +08:00 [DBG] info: 2025/6/23 17:32:00.524 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 17:32:00.584 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 17:32:00.599 +08:00 [DBG] info: 2025/6/23 17:32:00.599 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 17:32:00.622 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 17:32:08.236 +08:00 [INF] Application Shutting Down
2025-06-23 17:32:15.224 +08:00 [INF] Application Starting Up
2025-06-23 17:32:16.287 +08:00 [DBG] warn: 2025/6/23 17:32:16.287 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 17:32:16.460 +08:00 [DBG] info: 2025/6/23 17:32:16.460 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 17:32:16.465 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 17:32:16.637 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 17:32:16.656 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 17:32:19.859 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 17:32:19.864 +08:00 [INF] Getting topics for user: llk
2025-06-23 17:32:20.377 +08:00 [DBG] info: 2025/6/23 17:32:20.377 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 17:32:20.437 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 17:32:20.451 +08:00 [DBG] info: 2025/6/23 17:32:20.451 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 17:32:20.476 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 17:32:37.674 +08:00 [INF] Application Shutting Down
2025-06-23 17:32:43.954 +08:00 [INF] Application Starting Up
2025-06-23 17:32:45.053 +08:00 [DBG] warn: 2025/6/23 17:32:45.053 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 17:32:45.224 +08:00 [DBG] info: 2025/6/23 17:32:45.224 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (11ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 17:32:45.230 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 17:32:45.405 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 17:32:45.425 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 17:32:48.127 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 17:32:48.132 +08:00 [INF] Getting topics for user: llk
2025-06-23 17:32:48.635 +08:00 [DBG] info: 2025/6/23 17:32:48.635 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 17:32:48.696 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 17:32:48.711 +08:00 [DBG] info: 2025/6/23 17:32:48.711 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 17:32:48.734 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 17:33:18.300 +08:00 [INF] Application Shutting Down
2025-06-23 17:33:27.094 +08:00 [INF] Application Starting Up
2025-06-23 17:33:28.155 +08:00 [DBG] warn: 2025/6/23 17:33:28.155 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 17:33:28.331 +08:00 [DBG] info: 2025/6/23 17:33:28.331 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 17:33:28.337 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 17:33:28.509 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 17:33:28.529 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 17:33:31.208 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 17:33:31.214 +08:00 [INF] Getting topics for user: llk
2025-06-23 17:33:31.717 +08:00 [DBG] info: 2025/6/23 17:33:31.717 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 17:33:31.778 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 17:33:31.793 +08:00 [DBG] info: 2025/6/23 17:33:31.793 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 17:33:31.815 +08:00 [INF] Setting conversation history. Message count: 4
2025-06-23 17:33:58.134 +08:00 [INF] Application Shutting Down
2025-06-23 17:34:45.113 +08:00 [INF] Application Starting Up
2025-06-23 17:34:46.215 +08:00 [DBG] warn: 2025/6/23 17:34:46.214 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-06-23 17:34:46.392 +08:00 [DBG] info: 2025/6/23 17:34:46.392 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (12ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-06-23 17:34:46.398 +08:00 [INF] TopicService initialized and database ensured.
2025-06-23 17:34:46.577 +08:00 [INF] Initializing SemanticKernelService...
2025-06-23 17:34:46.597 +08:00 [INF] SemanticKernelService initialized.
2025-06-23 17:34:49.335 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-06-23 17:34:49.340 +08:00 [INF] Getting topics for user: llk
2025-06-23 17:34:49.845 +08:00 [DBG] info: 2025/6/23 17:34:49.845 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-06-23 17:34:49.905 +08:00 [INF] Getting messages for topic ID: 7
2025-06-23 17:34:49.921 +08:00 [DBG] info: 2025/6/23 17:34:49.921 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='7'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-06-23 17:34:49.944 +08:00 [INF] Setting conversation history. Message count: 4
