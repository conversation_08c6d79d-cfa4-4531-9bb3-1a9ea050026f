# 图片显示问题排查指南

## 问题描述
- AI助手消息不显示Markdown格式图片
- 用户消息都不显示图片

## 已修复的问题

### 1. ✅ 转换器问题
**问题**: 使用了错误的Visibility转换器
**修复**: 将 `BoolToVisibilityConverter` 改为 `BoolToVisibilityConverter2`

```xml
<!-- 修复前 -->
Visibility="{Binding HasImages, Converter={StaticResource BoolToVisibilityConverter}}"

<!-- 修复后 -->
Visibility="{Binding HasImages, Converter={StaticResource BoolToVisibilityConverter2}}"
```

**原因**: 
- `BoolToVisibilityConverter` 处理字符串角色（"user"/"assistant"）
- `BoolToVisibilityConverter2` 处理布尔值
- `HasImages` 属性返回布尔值

### 2. ✅ 正则表达式优化
**问题**: 原始正则表达式可能匹配不准确
**修复**: 分离了三种匹配模式，增加了调试输出

## 排查步骤

### 第一步：检查调试输出
运行应用程序并发送包含图片的消息，查看调试输出：

```
用户消息内容: ![测试](https://example.com/test.jpg)
提取到的图片URL数量: 1
  图片URL: https://example.com/test.jpg
```

### 第二步：测试图片URL提取
使用 `TestImageExtraction.cs` 测试图片URL提取功能：

```bash
dotnet run TestImageExtraction.cs
```

### 第三步：检查数据绑定
确认以下绑定是否正确：
- `ItemsSource="{Binding ImageUrls}"`
- `Visibility="{Binding HasImages, Converter={StaticResource BoolToVisibilityConverter2}}"`

### 第四步：验证ChatMessage属性
确认ChatMessage对象包含正确的数据：
- `ImageUrls` 列表不为空
- `HasImages` 返回 true

## 测试用例

### Markdown格式
```
![示例图片](https://picsum.photos/300/200?random=1)
```

### HTML格式
```
<img src="https://picsum.photos/350/250?random=3" alt="HTML图片" />
```

### 直接URL
```
https://picsum.photos/300/300?random=6
```

### 混合格式
```
这里有多种格式的图片：

Markdown格式：
![图片1](https://picsum.photos/200/150?random=9)

HTML格式：
<img src="https://picsum.photos/250/180?random=10" alt="图片2" />

直接URL：
https://picsum.photos/300/200?random=11
```

## 可能的剩余问题

### 1. 网络连接问题
- 确保有网络连接
- 测试图片URL是否可访问
- 检查防火墙设置

### 2. 图片格式问题
- 确认图片URL以支持的格式结尾
- 支持的格式：jpg, jpeg, png, gif, bmp, webp, svg

### 3. XAML渲染问题
- 检查WrapPanel是否正确加载
- 确认Image控件的Source绑定

### 4. 数据上下文问题
- 确认DataTemplate的数据上下文是ChatMessage
- 检查属性通知是否正常工作

## 调试技巧

### 1. 添加临时可见性测试
在XAML中临时添加一个总是可见的测试元素：

```xml
<!-- 临时测试元素 -->
<Border Background="Red" Width="100" Height="50" Margin="5">
    <TextBlock Text="{Binding ImageUrls.Count}" Foreground="White" 
               HorizontalAlignment="Center" VerticalAlignment="Center"/>
</Border>
```

### 2. 使用Snoop或其他WPF调试工具
- 检查可视化树
- 查看数据绑定状态
- 验证属性值

### 3. 简化测试
创建一个简单的测试消息：

```csharp
var testMessage = new ChatMessage
{
    Role = "assistant",
    Content = "测试图片",
    ImageUrls = new List<string> { "https://picsum.photos/200/200" },
    // ... 其他属性
};
```

## 下一步行动

1. **运行应用程序**并测试图片显示
2. **查看调试输出**确认URL提取是否正常
3. **使用测试用例**验证不同格式的图片
4. **检查网络连接**确保图片可以加载
5. **如果仍有问题**，使用WPF调试工具进一步排查

## 联系支持

如果问题仍然存在，请提供：
- 调试输出日志
- 测试用的图片URL
- 具体的错误信息
- 应用程序版本信息
