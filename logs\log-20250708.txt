2025-07-08 16:58:46.857 +08:00 [DBG] Hosting starting
2025-07-08 16:58:46.923 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-08 16:58:46.930 +08:00 [INF] Hosting environment: Production
2025-07-08 16:58:46.932 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai
2025-07-08 16:58:46.934 +08:00 [DBG] Hosting started
2025-07-08 16:58:46.935 +08:00 [INF] Application Starting Up
2025-07-08 16:58:47.971 +08:00 [DBG] warn: 2025/7/8 16:58:47.971 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-08 16:58:48.240 +08:00 [DBG] info: 2025/7/8 16:58:48.240 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (13ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-08 16:58:48.246 +08:00 [INF] TopicService initialized and database ensured.
2025-07-08 16:58:48.438 +08:00 [INF] Initializing SemanticKernelService...
2025-07-08 16:58:48.458 +08:00 [INF] SemanticKernelService initialized.
2025-07-08 16:58:51.652 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-08 16:58:51.658 +08:00 [INF] Getting topics for user: llk
2025-07-08 16:58:52.232 +08:00 [DBG] fail: 2025/7/8 16:58:52.232 RelationalEventId.CommandError[20102] (Microsoft.EntityFrameworkCore.Database.Command) 
      Failed executing DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-08 16:58:52.256 +08:00 [DBG] fail: 2025/7/8 16:58:52.255 CoreEventId.QueryIterationFailed[10100] (Microsoft.EntityFrameworkCore.Query) 
      An exception occurred while iterating over the results of a query for context type 'Yidev.LocalAI.Services.DatabaseContext'.
      Microsoft.Data.Sqlite.SqliteException (0x80004005): SQLite Error 1: 'no such column: t.UserName'.
         at Microsoft.Data.Sqlite.SqliteException.ThrowExceptionForRC(Int32 rc, sqlite3 db)
         at Microsoft.Data.Sqlite.SqliteCommand.PrepareAndEnumerateStatements()+MoveNext()
         at Microsoft.Data.Sqlite.SqliteCommand.GetStatements()+MoveNext()
         at Microsoft.Data.Sqlite.SqliteDataReader.NextResult()
         at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReader(CommandBehavior behavior)
         at Microsoft.Data.Sqlite.SqliteCommand.ExecuteReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
         at Microsoft.Data.Sqlite.SqliteCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
         at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
         at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
         at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
         at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
2025-07-08 16:59:49.461 +08:00 [DBG] Hosting starting
2025-07-08 16:59:49.524 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-08 16:59:49.531 +08:00 [INF] Hosting environment: Production
2025-07-08 16:59:49.533 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai
2025-07-08 16:59:49.535 +08:00 [DBG] Hosting started
2025-07-08 16:59:49.536 +08:00 [INF] Application Starting Up
2025-07-08 16:59:50.524 +08:00 [DBG] warn: 2025/7/8 16:59:50.524 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-08 16:59:50.702 +08:00 [DBG] info: 2025/7/8 16:59:50.702 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (15ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      PRAGMA journal_mode = 'wal';
2025-07-08 16:59:50.850 +08:00 [DBG] info: 2025/7/8 16:59:50.850 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      CREATE TABLE "Topics" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_Topics" PRIMARY KEY AUTOINCREMENT,
          "Name" TEXT NOT NULL,
          "CreatedAt" TEXT NOT NULL,
          "UserName" TEXT NOT NULL
      );
2025-07-08 16:59:50.853 +08:00 [DBG] info: 2025/7/8 16:59:50.853 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      CREATE TABLE "ChatMessages" (
          "Id" INTEGER NOT NULL CONSTRAINT "PK_ChatMessages" PRIMARY KEY AUTOINCREMENT,
          "Role" TEXT NOT NULL,
          "Content" TEXT NOT NULL,
          "Timestamp" TEXT NOT NULL,
          "TopicId" INTEGER NOT NULL,
          "SenderName" TEXT NULL,
          CONSTRAINT "FK_ChatMessages_Topics_TopicId" FOREIGN KEY ("TopicId") REFERENCES "Topics" ("Id") ON DELETE CASCADE
      );
2025-07-08 16:59:50.855 +08:00 [DBG] info: 2025/7/8 16:59:50.855 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      CREATE INDEX "IX_ChatMessages_TopicId" ON "ChatMessages" ("TopicId");
2025-07-08 16:59:50.864 +08:00 [INF] TopicService initialized and database ensured.
2025-07-08 16:59:51.046 +08:00 [INF] Initializing SemanticKernelService...
2025-07-08 16:59:51.069 +08:00 [INF] SemanticKernelService initialized.
2025-07-08 16:59:54.927 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-08 16:59:54.933 +08:00 [INF] Getting topics for user: llk
2025-07-08 16:59:55.423 +08:00 [DBG] info: 2025/7/8 16:59:55.423 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-08 17:03:01.529 +08:00 [DBG] Hosting starting
2025-07-08 17:03:01.592 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-08 17:03:01.599 +08:00 [INF] Hosting environment: Production
2025-07-08 17:03:01.602 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai
2025-07-08 17:03:01.603 +08:00 [DBG] Hosting started
2025-07-08 17:03:01.605 +08:00 [INF] Application Starting Up
2025-07-08 17:03:02.600 +08:00 [DBG] warn: 2025/7/8 17:03:02.599 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-08 17:03:02.780 +08:00 [DBG] info: 2025/7/8 17:03:02.780 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (13ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-08 17:03:02.786 +08:00 [INF] TopicService initialized and database ensured.
2025-07-08 17:03:02.972 +08:00 [INF] Initializing SemanticKernelService...
2025-07-08 17:03:02.991 +08:00 [INF] SemanticKernelService initialized.
2025-07-08 17:03:05.853 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-08 17:03:05.858 +08:00 [INF] Getting topics for user: llk
2025-07-08 17:03:06.386 +08:00 [DBG] info: 2025/7/8 17:03:06.386 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (6ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-08 17:03:06.397 +08:00 [INF] Creating topic '新话题 17:03:06' for user: llk
2025-07-08 17:03:06.562 +08:00 [DBG] info: 2025/7/8 17:03:06.562 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='2025-07-08T17:03:06.3997352+08:00' (DbType = DateTime), @p1='新话题 17:03:06' (Nullable = false) (Size = 12), @p2='llk' (Nullable = false) (Size = 3)], CommandType='Text', CommandTimeout='30']
      INSERT INTO "Topics" ("CreatedAt", "Name", "UserName")
      VALUES (@p0, @p1, @p2)
      RETURNING "Id";
2025-07-08 17:03:06.588 +08:00 [INF] Topic '新话题 17:03:06' created with ID: 1
2025-07-08 17:03:06.597 +08:00 [INF] Getting messages for topic ID: 1
2025-07-08 17:03:06.613 +08:00 [DBG] info: 2025/7/8 17:03:06.613 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='1'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-08 17:03:06.616 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-08 17:03:06.617 +08:00 [INF] Setting conversation history. Message count: 0
2025-07-08 17:03:06.621 +08:00 [INF] Adding message to topic ID: 1
2025-07-08 17:03:06.653 +08:00 [DBG] info: 2025/7/8 17:03:06.653 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (0ms) [Parameters=[@p0='# Markdown 测试
      
      这是一个 **粗体文本** 和 *斜体文本* 的示例。
      
      ## 代码示例
      
      这里是一些内联代码：`var x = 10;`
      
      ```csharp
      public class Example
      {
          public string Name { get; set; }
          public void PrintHello()
          {
              Console.WriteLine("Hello, World!");
          }
      }
      ```
      
      ## 列表示例
      
      ### 无序列表
      - 项目 1
      - 项目 2
        - 子项目 2.1
        - 子项目 2.2
      - 项目 3
      
      ### 有序列表
      1. 第一项
      2. 第二项
      3. 第三项
      
      ## 链接和引用
      
      这是一个 [链接示例](https://www.example.com)
      
      > 这是一个引用块
      > 可以包含多行内容
      
      ## 表格
      
      | 列1 | 列2 | 列3 |
      |-----|-----|-----|
      | 数据1 | 数据2 | 数据3 |
      | 数据4 | 数据5 | 数据6 |
      
      ---
      
      这就是 Markdown 渲染测试！' (Nullable = false) (Size = 562), @p1='assistant' (Nullable = false) (Size = 9), @p2='AI助手' (Size = 4), @p3='2025-07-08T17:03:06.6207142+08:00' (DbType = DateTime), @p4='1'], CommandType='Text', CommandTimeout='30']
      INSERT INTO "ChatMessages" ("Content", "Role", "SenderName", "Timestamp", "TopicId")
      VALUES (@p0, @p1, @p2, @p3, @p4)
      RETURNING "Id";
2025-07-08 17:05:01.079 +08:00 [DBG] Hosting starting
2025-07-08 17:05:01.143 +08:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-08 17:05:01.149 +08:00 [INF] Hosting environment: Production
2025-07-08 17:05:01.152 +08:00 [INF] Content root path: D:\yidev\Source\yidev.localai
2025-07-08 17:05:01.153 +08:00 [DBG] Hosting started
2025-07-08 17:05:01.155 +08:00 [INF] Application Starting Up
2025-07-08 17:05:02.128 +08:00 [DBG] warn: 2025/7/8 17:05:02.127 CoreEventId.SensitiveDataLoggingEnabledWarning[10400] (Microsoft.EntityFrameworkCore.Infrastructure) 
      Sensitive data logging is enabled. Log entries and exception messages may include sensitive application data; this mode should only be enabled during development.
2025-07-08 17:05:02.305 +08:00 [DBG] info: 2025/7/8 17:05:02.305 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (13ms) [Parameters=[], CommandType='Text', CommandTimeout='30']
      SELECT COUNT(*) FROM "sqlite_master" WHERE "type" = 'table' AND "rootpage" IS NOT NULL;
2025-07-08 17:05:02.311 +08:00 [INF] TopicService initialized and database ensured.
2025-07-08 17:05:02.495 +08:00 [INF] Initializing SemanticKernelService...
2025-07-08 17:05:02.514 +08:00 [INF] SemanticKernelService initialized.
2025-07-08 17:05:05.461 +08:00 [INF] MCP Filesystem 插件已添加: filesystem
2025-07-08 17:05:05.466 +08:00 [INF] Getting topics for user: llk
2025-07-08 17:05:05.988 +08:00 [DBG] info: 2025/7/8 17:05:05.988 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (5ms) [Parameters=[@__username_0='llk' (Size = 3)], CommandType='Text', CommandTimeout='30']
      SELECT "t"."Id", "t"."CreatedAt", "t"."Name", "t"."UserName"
      FROM "Topics" AS "t"
      WHERE "t"."UserName" = @__username_0
      ORDER BY "t"."CreatedAt" DESC
2025-07-08 17:05:06.058 +08:00 [INF] Getting messages for topic ID: 1
2025-07-08 17:05:06.074 +08:00 [DBG] info: 2025/7/8 17:05:06.074 RelationalEventId.CommandExecuted[20101] (Microsoft.EntityFrameworkCore.Database.Command) 
      Executed DbCommand (1ms) [Parameters=[@__topicId_0='1'], CommandType='Text', CommandTimeout='30']
      SELECT "c"."Id", "c"."Content", "c"."Role", "c"."SenderName", "c"."Timestamp", "c"."TopicId"
      FROM "ChatMessages" AS "c"
      WHERE "c"."TopicId" = @__topicId_0
      ORDER BY "c"."Timestamp"
2025-07-08 17:05:06.098 +08:00 [INF] User directory: D:/yidev/Source/yidev.localai/bin/Debug/net9.0-windows10.0.26100.0/uploads/llk
2025-07-08 17:05:06.100 +08:00 [INF] Setting conversation history. Message count: 1
2025-07-08 17:05:47.288 +08:00 [INF] Application Shutting Down
2025-07-08 17:05:47.292 +08:00 [DBG] Hosting stopping
2025-07-08 17:05:47.293 +08:00 [INF] Application is shutting down...
2025-07-08 17:05:47.295 +08:00 [DBG] Hosting stopped
