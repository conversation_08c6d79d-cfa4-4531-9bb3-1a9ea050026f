using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using Yidev.LocalAI.Services.Mcp;
using System.Reflection;

namespace Yidev.LocalAI.Models
{
    public class McpSettingsViewModel
    {
        public ObservableCollection<McpServiceItem> Services { get; } = new();

        public ICommand SaveCommand { get; }

        private readonly McpSettings _settings;

        public McpSettingsViewModel()
        {
            _settings = McpSettings.Load();
            LoadServices();
            SaveCommand = new RelayCommand(SaveSettings);
        }

        private void LoadServices()
        {
            Services.Clear();

            // 扫描当前程序集以及插件程序集中的所有 IMcpService 实现
            var types = new System.Collections.Generic.List<Type>();

            // 当前程序集
            var localAssembly = Assembly.GetExecutingAssembly();
            types.AddRange(localAssembly.GetTypes().Where(t => typeof(IMcpService).IsAssignableFrom(t) && !t.IsInterface && !t.IsAbstract));

            // 插件目录
            string mcpServicesPath = System.IO.Path.Combine(AppContext.BaseDirectory, "McpServices");
            if (System.IO.Directory.Exists(mcpServicesPath))
            {
                foreach (var dll in System.IO.Directory.GetFiles(mcpServicesPath, "*.dll"))
                {
                    try
                    {
                        var asm = Assembly.LoadFrom(dll);
                        types.AddRange(asm.GetTypes().Where(t => typeof(IMcpService).IsAssignableFrom(t) && !t.IsInterface && !t.IsAbstract));
                    }
                    catch
                    {
                        // ignore load failures
                    }
                }
            }

            // 去重
            types = types.Distinct().ToList();

            foreach (var type in types)
            {
                try
                {
                    if (Activator.CreateInstance(type) is IMcpService instance)
                    {
                        Services.Add(new McpServiceItem
                        {
                            Name = instance.Name,
                            Description = instance.Description,
                            IsEnabled = _settings.EnabledServiceNames.Count == 0 || _settings.EnabledServiceNames.Contains(instance.Name)
                        });
                    }
                }
                catch
                {
                    // ignore
                }
            }
        }

        private Task SaveSettings()
        {
            _settings.EnabledServiceNames = Services.Where(s => s.IsEnabled).Select(s => s.Name).ToList();
            _settings.Save();
            System.Windows.MessageBox.Show("设置已保存，需要重新启动应用才能生效。", "提示", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            return Task.CompletedTask;
        }
    }
} 